/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Add: typeof import('./src/components/toolbar/Add.vue')['default']
    ArticleContentDialog: typeof import('./src/components/dialog/ArticleContentDialog.vue')['default']
    ArticleList: typeof import('./src/components/list/ArticleList.vue')['default']
    Backgorund: typeof import('./src/components/Backgorund.vue')['default']
    BaseButton: typeof import('./src/components/BaseButton.vue')['default']
    BaseIcon: typeof import('./src/components/BaseIcon.vue')['default']
    BaseList: typeof import('./src/components/list/BaseList.vue')['default']
    ChapterName: typeof import('./src/components/toolbar/ChapterName.vue')['default']
    Close: typeof import('./src/components/icon/Close.vue')['default']
    CollectNotice: typeof import('./src/components/CollectNotice.vue')['default']
    Dialog: typeof import('./src/components/dialog/Dialog.vue')['default']
    DictDiglog: typeof import('./src/components/dialog/DictDiglog.vue')['default']
    DictGroup: typeof import('./src/components/list/DictGroup.vue')['default']
    DictItem: typeof import('./src/components/list/DictItem.vue')['default']
    DictList: typeof import('./src/components/list/DictList.vue')['default']
    DictListPanel: typeof import('./src/components/DictListPanel.vue')['default']
    EditAbleText: typeof import('./src/components/EditAbleText.vue')['default']
    EditArticle: typeof import('./src/components/article/EditArticle.vue')['default']
    EditBatchArticleModal: typeof import('./src/components/article/EditBatchArticleModal.vue')['default']
    EditSingleArticleModal: typeof import('./src/components/article/EditSingleArticleModal.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    Empty: typeof import('./src/components/Empty.vue')['default']
    FeedbackModal: typeof import('./src/components/toolbar/FeedbackModal.vue')['default']
    Fireworks: typeof import('./src/components/Fireworks.vue')['default']
    IconWrapper: typeof import('./src/components/IconWrapper.vue')['default']
    Input: typeof import('./src/components/Input.vue')['default']
    List: typeof import('./src/components/list/List.vue')['default']
    Logo: typeof import('./src/components/Logo.vue')['default']
    MiniDialog: typeof import('./src/components/dialog/MiniDialog.vue')['default']
    PopConfirm: typeof import('./src/components/PopConfirm.vue')['default']
    RepeatSetting: typeof import('./src/components/toolbar/RepeatSetting.vue')['default']
    RightTopBar: typeof import('./src/components/RightTopBar.vue')['default']
    Ring: typeof import('./src/components/Ring.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Setting: typeof import('./src/components/Setting.vue')['default']
    SettingDialog: typeof import('./src/components/dialog/SettingDialog.vue')['default']
    Slide: typeof import('./src/components/Slide.vue')['default']
    Toolbar: typeof import('./src/components/toolbar/index.vue')['default']
    Tooltip: typeof import('./src/components/Tooltip.vue')['default']
    TranslateSetting: typeof import('./src/components/toolbar/TranslateSetting.vue')['default']
    VolumeIcon: typeof import('./src/components/icon/VolumeIcon.vue')['default']
    VolumeSetting: typeof import('./src/components/toolbar/VolumeSetting.vue')['default']
    WordList: typeof import('./src/components/list/WordList.vue')['default']
    WordListDialog: typeof import('./src/components/dialog/WordListDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
