[{"name": "if", "trans": ["如果"]}, {"name": "else", "trans": ["否则"]}, {"name": "while", "trans": ["while 型循环"]}, {"name": "for", "trans": ["for 型循环"]}, {"name": "and", "trans": ["逻辑与运算符"]}, {"name": "or", "trans": ["逻辑或运算符"]}, {"name": "not", "trans": ["逻辑非运算符"]}, {"name": "TRUE", "trans": ["真"]}, {"name": "FALSE", "trans": ["假"]}, {"name": "None", "trans": ["空值"]}, {"name": "continue", "trans": ["跳出本次循环，继续下一轮循环"]}, {"name": "break", "trans": ["跳出整个循环"]}, {"name": "pass", "trans": ["空语句，不做任何事情"]}, {"name": "def", "trans": ["define的缩写，定义一个函数"]}, {"name": "return", "trans": ["返回语句，退出def语句块"]}, {"name": "global", "trans": ["声明全局变量"]}, {"name": "class", "trans": ["定义一个类"]}, {"name": "import", "trans": ["导入模块"]}, {"name": "from", "trans": ["与import配合导入模块"]}]