[{"name": "var", "trans": ["n. 用于声明变量"]}, {"name": "break", "trans": ["n. 用于提前结束循环或分支"]}, {"name": "default", "trans": ["n. 用于指定默认分支"]}, {"name": "func", "trans": ["n. 用于定义函数"]}, {"name": "interface", "trans": ["n. 用于定义接口"]}, {"name": "select", "trans": ["n. 用于选择通信操作"]}, {"name": "case", "trans": ["n. 用于指定分支情况"]}, {"name": "defer", "trans": ["n. 用于延迟函数执行"]}, {"name": "go", "trans": ["n. 用于并发执行函数"]}, {"name": "map", "trans": ["n. 用于声明映射数据结构"]}, {"name": "struct", "trans": ["n. 用于定义结构体"]}, {"name": "chan", "trans": ["n. 用于声明通道"]}, {"name": "else", "trans": ["n. 用于条件不成立时执行的分支"]}, {"name": "goto", "trans": ["n. 用于跳转到标签位置"]}, {"name": "package", "trans": ["n. 用于定义包"]}, {"name": "switch", "trans": ["n. 用于多条件选择"]}, {"name": "const", "trans": ["n. 用于声明常量"]}, {"name": "fallthrough", "trans": ["n. 用于执行下一个分支代码"]}, {"name": "if", "trans": ["n. 用于条件判断"]}, {"name": "range", "trans": ["n. 用于在循环中迭代元素"]}, {"name": "type", "trans": ["n. 用于定义自定义类型"]}, {"name": "continue", "trans": ["n. 用于跳过当前迭代"]}, {"name": "for", "trans": ["n. 用于循环"]}, {"name": "import", "trans": ["n. 用于导入外部包"]}, {"name": "return", "trans": ["n. 用于从函数中返回"]}]