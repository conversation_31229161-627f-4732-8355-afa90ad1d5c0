[{"name": "offerLast()", "trans": ["public boolean offerLast(E e):尾部插入元素，返回是否成功，成功为 true，失败为 false。"]}, {"name": "clear()", "trans": ["public void clear():清空链表。"]}, {"name": "removeFirst()", "trans": ["public E removeFirst():删除并返回第一个元素。"]}, {"name": "removeLast()", "trans": ["public E removeLast():删除并返回最后一个元素。"]}, {"name": "remove()", "trans": ["public boolean remove(Object o):删除某一元素，返回是否成功，成功为 true，失败为 false。"]}, {"name": "remove()", "trans": ["public E remove(int index):删除指定位置的元素。"]}, {"name": "poll()", "trans": ["public E poll():删除并返回第一个元素。"]}, {"name": "remove()", "trans": ["public E remove():删除并返回第一个元素。"]}, {"name": "contains()", "trans": ["public boolean contains(Object o):判断是否含有某一元素。"]}, {"name": "get()", "trans": ["public E get(int index):返回指定位置的元素。"]}, {"name": "getFirst()", "trans": ["public E getFirst():返回第一个元素。"]}, {"name": "getLast()", "trans": ["public E getLast():返回最后一个元素。"]}, {"name": "indexOf()", "trans": ["public int indexOf(Object o):查找指定元素从前往后第一次出现的索引。"]}, {"name": "lastIndexOf()", "trans": ["public int lastIndexOf(Object o):查找指定元素最后一次出现的索引。"]}, {"name": "peek()", "trans": ["public E peek():返回第一个元素。"]}, {"name": "element()", "trans": ["public E element():返回第一个元素。"]}, {"name": "peek<PERSON>irst()", "trans": ["public E peekFirst():返回头部元素。"]}, {"name": "peekLast()", "trans": ["public E peekLast():返回尾部元素。"]}, {"name": "set()", "trans": ["public E set(int index, E element):设置指定位置的元素。"]}, {"name": "clone()", "trans": ["public Object clone():克隆该列表。"]}, {"name": "descendingIterator()", "trans": ["public Iterator descendingIterator():返回倒序迭代器。"]}, {"name": "size()", "trans": ["public int size():返回链表元素个数。"]}, {"name": "listIterator()", "trans": ["public ListIterator listIterator(int index):返回从指定位置开始到末尾的迭代器。"]}, {"name": "toArray()", "trans": ["public Object[] toArray():返回一个由链表元素组成的数组。"]}, {"name": "toArray()", "trans": ["public  T[] toArray(T[] a):返回一个由链表元素转换类型而成的数组。"]}]