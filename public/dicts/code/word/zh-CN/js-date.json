[{"name": "Date.UTC()", "trans": ["Date.UTC() 方法接受的参数同日期构造函数接受最多参数时一样，返回从1970-1-1 00:00:00 UTC到指定日期的的毫秒数。"]}, {"name": "Date.now()", "trans": ["Date.now() 方法返回自1970年1月1日 00:00:00 UTC到当前时间的毫秒数。"]}, {"name": "Date.parse()", "trans": ["Date.parse() 方法解析一个表示某个日期的字符串，并返回从1970-1-1 00:00:00 UTC 到该日期对象（该日期对象的UTC时间）的毫秒数，如果该字符串无法识别，或者一些情况下，包含了不合法的日期数值（如：2015-02-31），则返回值为NaN。"]}, {"name": "getDate()", "trans": ["根据本地时间，返回一个指定的日期对象为一个月中的第几天。"]}, {"name": "getDay()", "trans": ["getDay() 方法根据本地时间，返回一个具体日期中一周的第几天，0 表示星期天。"]}, {"name": "getFullYear()", "trans": ["getFullYear() 方法根据本地时间返回指定日期的年份。"]}, {"name": "getHours()", "trans": ["getHours() 方法根据本地时间，返回一个指定的日期对象的小时。"]}, {"name": "getMilliseconds()", "trans": ["getMilliseconds() 方法，根据本地时间，返回一个指定的日期对象的毫秒数。"]}, {"name": "getMinutes()", "trans": ["getMinutes() 方法根据本地时间，返回一个指定的日期对象的分钟数。"]}, {"name": "getMonth()", "trans": ["根据本地时间，返回一个指定的日期对象的月份，为基于0的值（0表示一年中的第一月）。"]}, {"name": "getSeconds()", "trans": ["getSeconds() 方法根据本地时间，返回一个指定的日期对象的秒数。"]}, {"name": "getTime()", "trans": ["getTime() 方法返回一个时间的格林威治时间数值。"]}, {"name": "getTimezoneOffset()", "trans": ["getTimezoneOffset() 方法返回协调世界时（UTC）相对于当前时区的时间差值，单位为分钟。"]}, {"name": "getYear()", "trans": ["getYear() 方法返回指定的本地日期的年份。因为 getYear 不返回千禧年[full years] ('year 2000 problem')，所以这个方法不再被使用，现在替换为 getFullYear ."]}, {"name": "setDate()", "trans": ["setDate() 方法根据本地时间来指定一个日期对象的天数。"]}, {"name": "set<PERSON><PERSON><PERSON>ear()", "trans": ["setFullYear() 方法根据本地时间为一个日期对象设置年份。"]}, {"name": "setHours()", "trans": ["setHours() 方法根据本地时间为一个日期对象设置小时数，返回从1970-01-01 00:00:00 UTC 到更新后的 日期 对象实例所表示时间的毫秒数。"]}, {"name": "setMilliseconds()", "trans": ["setMilliseconds() 方法会根据本地时间设置一个日期对象的豪秒数。"]}, {"name": "setMinutes()", "trans": ["setMinutes() 方法根据本地时间为一个日期对象设置分钟数。"]}, {"name": "setMonth()", "trans": ["setMonth() 方法根据本地时间为一个设置年份的日期对象设置月份。"]}, {"name": "setSeconds()", "trans": ["setSeconds() 方法根据本地时间设置一个日期对象的秒数。"]}, {"name": "setTime()", "trans": ["setTime() 方法以一个表示从1970-1-1 00:00:00 UTC计时的毫秒数为来为 Date 对象设置时间。"]}, {"name": "setYear()", "trans": ["The setYear() method sets the year for a specified date according to local time. Because setYear does not set full years ('year 2000 problem'), it is no longer used and has been replaced by the setFullYear method."]}, {"name": "toDateString()", "trans": ["toDateString() 方法以美式英语和人类易读的形式返回一个日期对象日期部分的字符串。"]}, {"name": "toGMTString()", "trans": ["The toGMTString() method converts a date to a string, using Internet GMT conventions. The exact format of the value returned by toGMTString varies according to the platform and browser, in general it should represent a human readable date string."]}, {"name": "toISOString()", "trans": ["toISOString() 方法返回一个 ISO（ISO 8601 Extended Format）格式的字符串： YYYY-MM-DDTHH:mm:ss.sssZ。时区总是UTC（协调世界时），加一个后缀“Z”标识。"]}, {"name": "toJSON()", "trans": ["toJSON() 方法返回 Date 对象的字符串形式。"]}, {"name": "toLocaleDateString()", "trans": ["toLocaleDateString() 方法返回该日期对象日期部分的字符串，该字符串格式因不同语言而不同。新增的参数 locales 和 options 使程序能够指定使用哪种语言格式化规则，允许定制该方法的表现（behavior）。在旧版本浏览器中， locales 和 options 参数被忽略，使用的语言环境和返回的字符串格式是各自独立实现的。"]}, {"name": "toLocaleFormat()", "trans": ["非标准方法 toLocaleFormat() 按特定的格式将一个日期转换成一个字符串。 Intl.DateTimeFormat 是符合标准的格式化日期的替代方法。另见更新的(newer)版本的 toLocaleDateString()方法."]}, {"name": "toLocaleString()", "trans": ["toLocaleString() 方法返回该日期对象的字符串，该字符串格式因不同语言而不同。新增的参数 locales 和 options 使程序能够指定使用哪种语言格式化规则，允许定制该方法的表现（behavior）。在旧版本浏览器中， locales 和 options 参数被忽略，使用的语言环境和返回的字符串格式是各自独立实现的。"]}, {"name": "toLocaleTimeString()", "trans": ["The toLocaleTimeString() 方法返回该日期对象时间部分的字符串，该字符串格式因不同语言而不同。新增的参数 locales 和 options 使程序能够指定使用哪种语言格式化规则，允许定制该方法的表现（behavior）。在旧版本浏览器中， locales 和 options 参数被忽略，使用的语言环境和返回的字符串格式是各自独立实现的。"]}, {"name": "toSource()", "trans": ["toSource() 返回表示源代码的字符串。"]}, {"name": "toString()", "trans": ["toString() 方法返回一个字符串，表示该Date对象。"]}, {"name": "toTimeString()", "trans": ["toTimeString() 方法以人类易读形式返回一个日期对象时间部分的字符串，该字符串以美式英语格式化。"]}]