[{"name": "Capacity", "trans": ["Capacity:获取或设置该内部数据结构在不调整大小的情况下能够容纳的元素总数"]}, {"name": "Count", "trans": ["Count:获取 List<T> 中包含的元素数"]}, {"name": "item[]", "trans": ["item[]:获取或设置指定索引处的元素"]}, {"name": "Add()", "trans": ["Add(T):将对象添加到 List<T> 的结尾处"]}, {"name": "AddRange()", "trans": ["AddRange(IEnumerable<T>):将指定集合的元素添加到 List<T> 的末尾"]}, {"name": "AsReadOnly()", "trans": ["AsReadOnly():返回当前集合的只读 ReadOnlyCollection<T> 包装器"]}, {"name": "BinarySearch()", "trans": ["BinarySearch(T):使用默认的比较器在整个已排序的 List<T> 中搜索元素，并返回该元素从零开始的索引", "BinarySearch(T, <PERSON><PERSON><PERSON>parer<T>):删除 arraylist 里的单个元素", "BinarySearch(Int32, Int32, T, IComparer<T>):使用指定的比较器在已排序的 List<T> 的某个元素范围中搜索元素，并返回该元素从零开始的索引"]}, {"name": "Clear()", "trans": ["Clear():从 List<T> 中移除所有元素"]}, {"name": "Contains()", "trans": ["Contains(T):确定某元素是否在 List<T> 中"]}, {"name": "ConvertAll()", "trans": ["ConvertAll<TOutput>(Converter<T,TOutput>):将当前 List<T> 中的元素转换为另一种类型，并返回包含已转换元素的列表"]}, {"name": "CopyTo()", "trans": ["CopyTo(T[]):从目标数组的开头开始，将整个 List<T> 复制到兼容的一维数组", "CopyTo(T[], Int32):从目标数组的指定索引处开始，将整个 List<T> 复制到兼容的一维数组", "CopyTo(Int32, T[], Int32, Int32):从目标数组的指定索引处开始，将元素的范围从 List<T> 复制到兼容的一维数组"]}, {"name": "Equals()", "trans": ["Equals(Object):确定指定对象是否等于当前对象（继承自 Object）"]}, {"name": "Exists()", "trans": ["Exists(Predicate<T>):确定 List<T> 是否包含与指定谓词定义的条件匹配的元素"]}, {"name": "Find()", "trans": ["Find(Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回整个 List<T> 中的第一个匹配元素"]}, {"name": "FindAll()", "trans": ["FindAll(Predicate<T>):检索与指定谓词定义的条件匹配的所有元素"]}, {"name": "FindIndex()", "trans": ["FindIndex(Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回整个 List<T> 中第一个匹配元素的从零开始的索引", "FindIndex(Int32, Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回 List<T> 中从指定索引到最后一个元素的元素范围内第一个匹配项的从零开始的索引", "FindIndex(Int32, Int32, Predicate<T>):搜索与指定谓词所定义的条件相匹配的一个元素，并返回 List<T> 中从指定的索引开始、包含指定元素个数的元素范围内第一个匹配项的从零开始的索引"]}, {"name": "FindLast()", "trans": ["FindLast(Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回整个 List<T> 中的最后一个匹配元素"]}, {"name": "FindLastIndex()", "trans": ["FindLastIndex(Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回整个 List<T> 中最后一个匹配元素的从零开始的索引", "FindLastIndex(Int32, Predicate<T>):搜索与由指定谓词定义的条件相匹配的元素，并返回 List<T> 中从第一个元素到指定索引的元素范围内最后一个匹配项的从零开始的索引", "FindLastIndex(Int32, Int32, Predicate<T>):搜索与指定谓词所定义的条件相匹配的元素，并返回 List<T> 中包含指定元素个数、到指定索引结束的元素范围内最后一个匹配项的从零开始的索引"]}, {"name": "ForEach()", "trans": ["ForEach(Action<T>):对 List<T> 的每个元素执行指定操作"]}, {"name": "GetEnumerator()", "trans": ["GetEnumerator():返回循环访问 List<T> 的枚举数"]}, {"name": "GetHashCode()", "trans": ["GetHashCode():作为默认哈希函数（继承自 Object）"]}, {"name": "GetRange()", "trans": ["GetRange(Int32, Int32):在源 List<T> 中创建元素范围的浅表复制"]}, {"name": "GetType()", "trans": ["GetType():获取当前实例的 Type（继承自 Object）"]}, {"name": "IndexOf()", "trans": ["IndexOf(T):搜索指定的对象，并返回整个 List<T> 中第一个匹配项的从零开始的索引", "IndexOf(T, Int32):搜索指定对象并返回 List<T> 中从指定索引到最后一个元素这部分元素中第一个匹配项的从零开始索引", "IndexOf(T, Int32, Int32):搜索指定对象并返回 List<T> 中从指定索引开始并包含指定元素数的这部分元素中第一个匹配项的从零开始索引"]}, {"name": "Insert()", "trans": ["Insert(Int32, T):将元素插入 List<T> 的指定索引处"]}, {"name": "InsertRange()", "trans": ["InsertRange(Int32, IEnumerable<T>):将集合中的元素插入 List<T> 的指定索引处"]}, {"name": "LastIndexOf()", "trans": ["LastIndexOf(T):搜索指定对象并返回整个 List<T> 中最后一个匹配项的从零开始索引", "LastIndexOf(T, Int32):搜索指定对象并返回 List<T> 中从第一个元素到指定索引这部分元素中最后一个匹配项的从零开始的索引", "LastIndexOf(T, Int32, Int32):搜索指定对象并返回 List<T> 中到指定索引为止包含指定元素数的这部分元素中最后一个匹配项的从零开始索引"]}, {"name": "MemberwiseClone()", "trans": ["MemberwiseClone():创建当前 Object 的浅表副本（继承自 Object）"]}, {"name": "Remove()", "trans": ["Remove(T):从 List<T> 中移除特定对象的第一个匹配项"]}, {"name": "RemoveAll()", "trans": ["RemoveAll(Predicate<T>):移除与指定的谓词所定义的条件相匹配的所有元素", "RemoveAt(Int32):移除 List<T> 的指定索引处的元素", "RemoveRange(Int32, Int32):从 List<T> 中移除一系列元素"]}, {"name": "Reverse()", "trans": ["Reverse(Int32, Int32):将指定范围中元素的顺序反转"]}, {"name": "Sort()", "trans": ["Sort():使用默认比较器对整个 List<T> 中的元素进行排序", "Sort(Comparison<T>):使用指定的 Comparison<T>，对整个 List<T> 中的元素进行排序", "Sort(IComparer<T>):使用指定的比较器对整个 List<T> 中的元素进行排序"]}, {"name": "ToArray()", "trans": ["将 List<T> 的元素复制到新数组中"]}, {"name": "ToString()", "trans": ["返回表示当前对象的字符串（继承自 Object）"]}, {"name": "TrimExcess()", "trans": ["TrimExcess():将容量设置为 List<T> 中元素的实际数目（如果该数目小于某个阈值）"]}, {"name": "TrueForAll()", "trans": ["TrueForAll(Predicate<T>):确定 List<T> 中的每个元素是否都与指定谓词定义的条件匹配"]}]