[{"name": "math.ceil(x)", "trans": ["返回数字的上入整数（返回传入数字的向上取整的值）"]}, {"name": "math.copysign(x,y)", "trans": ["方法返回一个浮点数，该浮点数由第一个参数的值和第二个参数的正负号（+/-）组成"]}, {"name": "math.fabs(x)", "trans": ["返回浮点型数字的绝对值，如​math.fabs(-10) ​返回10.0。"]}, {"name": "math.factorial(x)", "trans": ["返回数字的阶乘"]}, {"name": "math.floor(x)", "trans": ["返回数字的下舍整数（向下取整的浮点型整数）"]}, {"name": "math.fmod(x,y)", "trans": ["返回x/y的余数（模）"]}, {"name": "math.frexp(x)", "trans": ["以成对（m，e）的形式返回尾数和指定数字的指数"]}, {"name": "math.fsum(iterable)", "trans": ["返回所有项目的总和"]}, {"name": "math.isinf(x)", "trans": ["检查值是否为无穷大"]}, {"name": "math.isnan(x)", "trans": ["检查值是否为NaN"]}, {"name": "math.ldexp(x,i)", "trans": ["返回给定数字x和i的x * (2 ** i)，这是math.frexp()的反函数"]}, {"name": "math.modf(x)", "trans": ["返回x的整数部分与小数部分，两部分的数值符号与x相同，整数部分以浮点型表示"]}, {"name": "math.trunc()", "trans": ["返回数字的截断的整数部分"]}, {"name": "math.exp(x)", "trans": ["返回x的指数,ex"]}, {"name": "math.log(x[,base])", "trans": ["返回数字的自然对数，或数字以对数为底的对数"]}, {"name": "math.log10(x)", "trans": ["返回数字的以10为底的对数"]}, {"name": "math.log1p(x)", "trans": ["返回log(1 + number)，即使在number的值接近于零的情况下，其计算方式也是准确的"]}, {"name": "math.pow(x,y)", "trans": ["方法返回 xy（x的y次方） 的值。"]}, {"name": "math.sqrt(x)", "trans": ["​方法返回数字x的平方根"]}, {"name": "math.acos(x)", "trans": ["返回x的反余弦弧度值"]}, {"name": "math.asin(x)", "trans": ["返回x的反正弦弧度值。"]}, {"name": "math.atan(x)", "trans": ["返回x的反正切弧度值。"]}, {"name": "math.atan2(y,x)", "trans": ["返回给定的 X 及 Y 坐标值的反正切值。"]}, {"name": "math.cos(x)", "trans": ["返回x的弧度的余弦值"]}, {"name": "math.hypot(x,y)", "trans": ["返回欧几里德范数 sqrt(x*x + y*y)"]}, {"name": "math.sin(x)", "trans": ["返回的x弧度的正弦值"]}, {"name": "math.tan(x)", "trans": ["返回x弧度的正弦值"]}, {"name": "math.degrees(x)", "trans": ["弧度转换为角度"]}, {"name": "math.radians(x)", "trans": ["将角度转换为弧度"]}, {"name": "math.acosh(x)", "trans": ["返回数字的反双曲余弦值"]}, {"name": "math.asinh(x)", "trans": ["返回数字的反双曲正弦"]}, {"name": "math.atanh(x)", "trans": ["返回数字的反双曲正切值"]}, {"name": "math.cosh(x)", "trans": ["求不同数的双曲余弦"]}, {"name": "math.sinh(x)", "trans": ["查找不同数字的双曲正弦值"]}, {"name": "math.tanh(x)", "trans": ["返回不同数的双曲正切"]}, {"name": "math.e", "trans": ["数学常量 e，e即自然常数（自然常数）。"]}, {"name": "math.pi", "trans": ["数学常量 pi（圆周率，一般以π来表示）。"]}]