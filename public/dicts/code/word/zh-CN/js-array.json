[{"name": "Array.from()", "trans": ["Array.from() 方法从一个类似数组或可迭代对象中创建一个新的数组实例。"]}, {"name": "Array.isArray()", "trans": ["Array.isArray() 用于确定传递的值是否是一个 Array。"]}, {"name": "Array.observe()", "trans": ["Array.observe() 方法用于异步监视数组发生的变化，类似于针对对象的 Object.observe() 。当数组的值发生变化时，它按发生顺序提供了一个变化流。与 Object.observe() 类似，它由如下可接受的变化类型列表['add'、'update'、'delete'、'splice']触发。"]}, {"name": "Array.of()", "trans": ["Array.of() 方法创建一个具有可变数量参数的新数组实例，而不考虑参数的数量或类型。"]}, {"name": "concat()", "trans": ["concat() 方法用于合并两个或多个数组。此方法不会更改现有数组，而是返回一个新数组。"]}, {"name": "copyWithin()", "trans": ["改变了的数组。"]}, {"name": "entries()", "trans": ["entries() 方法返回一个新的Array Iterator对象，该对象包含数组中每个索引的键/值对。"]}, {"name": "every()", "trans": ["every() 方法测试数组的所有元素是否都通过了指定函数的测试。"]}, {"name": "fill()", "trans": ["fill() 方法用一个固定值填充一个数组中从起始索引到终止索引内的全部元素。"]}, {"name": "filter()", "trans": ["filter() 方法创建一个新数组, 其包含通过所提供函数实现的测试的所有元素。"]}, {"name": "find()", "trans": ["find() 方法返回数组中满足提供的测试函数的第一个元素的值。否则返回 undefined。"]}, {"name": "findIndex()", "trans": ["findIndex()方法返回数组中满足提供的测试函数的第一个元素的索引。否则返回-1。"]}, {"name": "flat()", "trans": ["flat() 方法会递归到指定深度将所有子数组连接，并返回一个新数组。"]}, {"name": "flatMap()", "trans": ["flatMap()方法首先使用映射函数映射每个元素，然后将结果压缩成一个新数组。它与 map 和 深度值1的 flatten 几乎相同，但flatMap通常在合并成一种方法的效率稍微高一些。"]}, {"name": "forEach()", "trans": ["forEach() 方法对数组的每个元素执行一次提供的函数。"]}, {"name": "includes()", "trans": ["includes() 方法用来判断一个数组是否包含一个指定的值，根据情况，如果包含则返回 true，否则返回false。"]}, {"name": "indexOf()", "trans": ["indexOf()方法返回在数组中可以找到一个给定元素的第一个索引，如果不存在，则返回-1。"]}, {"name": "join()", "trans": ["join() 方法将一个数组（或一个类数组对象）的所有元素连接成一个字符串并返回这个字符串。"]}, {"name": "keys()", "trans": ["keys() 方法返回一个新的Array迭代器，它包含数组中每个索引的键。"]}, {"name": "lastIndexOf()", "trans": ["lastIndexOf() 方法返回指定元素（也即有效的 JavaScript 值或变量）在数组中的最后一个的索引，如果不存在则返回 -1。从数组的后面向前查找，从 fromIndex 处开始。"]}, {"name": "map()", "trans": ["map() 方法创建一个新数组，其结果是该数组中的每个元素都调用一个提供的函数后返回的结果。"]}, {"name": "pop()", "trans": ["pop()方法从数组中删除最后一个元素，并返回该元素的值。此方法更改数组的长度。"]}, {"name": "push()", "trans": ["push() 方法将一个或多个元素添加到数组的末尾，并返回新数组的长度。"]}, {"name": "reduce()", "trans": ["reduce() 方法对累加器和数组中的每个元素（从左到右）应用一个函数，将其减少为单个值。"]}, {"name": "reduceRight()", "trans": ["reduceRight() 方法接受一个函数作为累加器（accumulator）和数组的每个值（从右到左）将其减少为单个值。"]}, {"name": "reverse()", "trans": ["reverse() 方法将数组中元素的位置颠倒。"]}, {"name": "shift()", "trans": ["shift() 方法从数组中删除第一个元素，并返回该元素的值。此方法更改数组的长度。"]}, {"name": "slice()", "trans": ["slice() 方法返回一个从开始到结束（不包括结束）选择的数组的一部分浅拷贝到一个新数组对象。且原始数组不会被修改。"]}, {"name": "some()", "trans": ["some() 方法测试数组中的某些元素是否通过由提供的函数实现的测试。"]}, {"name": "sort()", "trans": ["sort() 方法用就地（ in-place ）的算法对数组的元素进行排序，并返回数组。 sort 排序不一定是稳定的。默认排序顺序是根据字符串Unicode码点。"]}, {"name": "splice()", "trans": ["splice() 方法通过删除现有元素和/或添加新元素来更改一个数组的内容。"]}, {"name": "toLocaleString()", "trans": ["toLocaleString() 返回一个字符串表示数组中的元素。数组中的元素将使用各自的 toLocaleString 方法转成字符串，这些字符串将使用一个特定语言环境的字符串（例如一个逗号 ','）隔开。"]}, {"name": "toSource()", "trans": ["返回一个字符串,代表该数组的源代码."]}, {"name": "toString()", "trans": ["toString() 返回一个字符串，表示指定的数组及其元素。"]}, {"name": "unshift()", "trans": ["unshift() 方法将一个或多个元素添加到数组的开头，并返回新数组的长度。"]}, {"name": "values()", "trans": ["values() 方法返回一个新的 Array Iterator 对象，该对象包含数组每个索引的值"]}]