[{"name": "ab", "trans": ["Apache服务器的性能测试工具"]}, {"name": "accept", "trans": ["指示打印系统接受发往指定目标打印机的打印任务"]}, {"name": "ack", "trans": ["比grep好用的文本搜索工具"]}, {"name": "alias", "trans": ["定义或显示别名。"]}, {"name": "apachectl", "trans": ["Apache服务器前端控制工具"]}, {"name": "apk", "trans": ["Alpine Linux 下的包管理工具"]}, {"name": "apropos", "trans": ["在 whatis 数据库中查找字符串"]}, {"name": "apt-get", "trans": ["Debian Linux发行版中的APT软件包管理工具"]}, {"name": "apt-key", "trans": ["管理Debian Linux系统中的软件包密钥"]}, {"name": "apt-sortpkgs", "trans": ["Debian Linux下对软件包索引文件进行排序的工具"]}, {"name": "aptitude", "trans": ["Debian Linux系统中软件包管理工具"]}, {"name": "ar", "trans": ["建立或修改备存文件，或是从备存文件中抽取文件"]}, {"name": "arch", "trans": ["显示当前主机的硬件架构类型"]}, {"name": "arj", "trans": ["用于创建和管理.arj压缩包"]}, {"name": "arp", "trans": ["arp 命令用于显示和修改 IP 到 MAC 转换表"]}, {"name": "arpd", "trans": ["收集免费ARP信息"]}, {"name": "arping", "trans": ["通过发送ARP协议报文测试网络"]}, {"name": "arptables", "trans": ["管理ARP包过滤规则表"]}, {"name": "arpwatch", "trans": ["监听网络上ARP的记录"]}, {"name": "as", "trans": ["汇编语言编译器"]}, {"name": "at", "trans": ["在指定时间执行一个任务"]}, {"name": "atop", "trans": ["监控Linux系统资源与进程的工具"]}, {"name": "atq", "trans": ["列出当前用户的at任务列表"]}, {"name": "atrm", "trans": ["删除待执行任务队列中的指定任务"]}, {"name": "awk", "trans": ["文本和数据进行处理的编程语言"]}, {"name": "axel", "trans": ["多线程下载工具"]}, {"name": "badblocks", "trans": ["查找磁盘中损坏的区块"]}, {"name": "base64", "trans": ["base64 编码/解码文件或标准输入输出"]}, {"name": "basename", "trans": ["打印目录或者文件的基本名称"]}, {"name": "batch", "trans": ["在系统不繁忙的时候执行定时任务"]}, {"name": "bc", "trans": ["算术操作精密运算工具"]}, {"name": "bg", "trans": ["将前台终端作业移动到后台运行"]}, {"name": "bind", "trans": ["显示或设置键盘按键与其相关的功能"]}, {"name": "blkid", "trans": ["查看块设备的文件系统类型、LABEL、UUID等信息"]}, {"name": "blockdev", "trans": ["从命令行调用区块设备控制程序"]}, {"name": "bmodinfo", "trans": ["显示给定模块的详细信息"]}, {"name": "break", "trans": ["结束for，while或until循环。"]}, {"name": "builtin", "trans": ["执行bash内建命令。"]}, {"name": "bunzip2", "trans": ["创一个bz2文件压缩包"]}, {"name": "bye", "trans": ["命令用于中断FTP连线并结束程序"]}, {"name": "bzcat", "trans": ["解压缩指定的.bz2文件"]}, {"name": "bzcmp", "trans": ["比较两个压缩包中的文件"]}, {"name": "bzdiff", "trans": ["直接比较两个.bz2压缩包中文件的不同"]}, {"name": "bzgrep", "trans": ["使用正则表达式搜索.bz2压缩包中文件"]}, {"name": "bzip2", "trans": ["将文件压缩成bz2格式"]}, {"name": "bzip2recover", "trans": ["恢复被破坏的.bz2压缩包中的文件"]}, {"name": "bzless", "trans": ["增强.bz2压缩包查看器"]}, {"name": "bz<PERSON>", "trans": ["查看bzip2压缩过的文本文件的内容"]}, {"name": "cal", "trans": ["显示当前日历或指定日期的日历"]}, {"name": "cancel", "trans": ["取消已存在的打印任务"]}, {"name": "cat", "trans": ["连接多个文件并打印到标准输出。"]}, {"name": "cd", "trans": ["切换用户当前工作目录。"]}, {"name": "cdrecord", "trans": ["Linux系统下光盘刻录功能命令"]}, {"name": "chage", "trans": ["修改帐号和密码的有效期限"]}, {"name": "chattr", "trans": ["用来改变文件属性"]}, {"name": "chcon", "trans": ["修改对象（文件）的安全上下文"]}, {"name": "chfn", "trans": ["用来改变finger命令显示的信息"]}, {"name": "chgrp", "trans": ["用来变更文件或目录的所属群组"]}, {"name": "chkconfig", "trans": ["检查或设置系统的各种服务"]}, {"name": "chmod", "trans": ["用来变更文件或目录的权限"]}, {"name": "chown", "trans": ["用来变更文件或目录的拥有者或所属群组"]}, {"name": "chpasswd", "trans": ["批量更新用户口令的工具"]}, {"name": "chroot", "trans": ["把根目录换成指定的目的目录"]}, {"name": "chsh", "trans": ["用来更换登录系统时使用的shell"]}, {"name": "cksum", "trans": ["检查文件的CRC是否正确"]}, {"name": "clear", "trans": ["清除当前屏幕终端上的任何信息"]}, {"name": "clock", "trans": ["用于调整 RTC 时间"]}, {"name": "clockdiff", "trans": ["检测两台linux主机的时间差"]}, {"name": "cmp", "trans": ["比较两个文件是否有差异"]}, {"name": "col", "trans": ["过滤控制字符"]}, {"name": "colrm", "trans": ["删除文件中的指定列"]}, {"name": "comm", "trans": ["按行比较两个已排序的文件。"]}, {"name": "command", "trans": ["调用并执行指定的命令"]}, {"name": "compress", "trans": ["使用Lempress-Ziv编码压缩数据文件"]}, {"name": "consoletype", "trans": ["输出已连接的终端类型"]}, {"name": "continue", "trans": ["结束本次循环，继续执行下一个for，while或until循环。"]}, {"name": "convertquota", "trans": ["把老的配额文件转换为新的格式"]}, {"name": "cp", "trans": ["将源文件或目录复制到目标文件或目录中"]}, {"name": "cpio", "trans": ["用来建立、还原备份档的工具程序"]}, {"name": "crontab", "trans": ["提交和管理用户的需要周期性执行的任务"]}, {"name": "csplit", "trans": ["将一个大文件分割成小的碎片文件"]}, {"name": "cu", "trans": ["用于连接另一个系统主机"]}, {"name": "cupsdisable", "trans": ["停止指定的打印机"]}, {"name": "cupsenable", "trans": ["启动指定的打印机"]}, {"name": "curl", "trans": ["利用URL规则在命令行下工作的文件传输工具"]}, {"name": "cut", "trans": ["连接文件并打印到标准输出设备上"]}, {"name": "date", "trans": ["显示或设置系统时间与日期"]}, {"name": "dd", "trans": ["复制文件并对原文件的内容进行转换和格式化处理"]}, {"name": "declare", "trans": ["声明变量，设置或显示变量的值和属性。"]}, {"name": "depmod", "trans": ["分析可载入模块的相依性"]}, {"name": "df", "trans": ["显示磁盘的相关信息"]}, {"name": "dhclient", "trans": ["动态获取或释放IP地址"]}, {"name": "dhcpd", "trans": ["运行DHCP服务器"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["使用dhcrelay命令可以提供中继DHCP和BOOTP请求"]}, {"name": "diff", "trans": ["比较给定的两个文件的不同"]}, {"name": "diff3", "trans": ["比较3个文件不同的地方"]}, {"name": "diffstat", "trans": ["显示diff命令输出信息的柱状图"]}, {"name": "dig", "trans": ["域名查询工具"]}, {"name": "dircolors", "trans": ["置ls命令在显示目录或文件时所用的色彩"]}, {"name": "dirname", "trans": ["去除文件名中的非目录部分"]}, {"name": "dirs", "trans": ["显示目录堆栈。"]}, {"name": "disown", "trans": ["从当前的shell中移除作业。"]}, {"name": "dmesg", "trans": ["显示Linux系统启动信息"]}, {"name": "dmidecode", "trans": ["在Linux系统下获取有关硬件方面的信息"]}, {"name": "dnf", "trans": ["新一代的RPM软件包管理器"]}, {"name": "dnsdomainname", "trans": ["定义DNS系统中FQDN名称的域名"]}, {"name": "domainname", "trans": ["显示和设置系统的NIS域名"]}, {"name": "dos2unix", "trans": ["将DOS格式文本文件转换成Unix格式"]}, {"name": "dpkg-deb", "trans": ["Debian Linux下的软件包管理工具"]}, {"name": "dpkg-divert", "trans": ["Debian Linux中创建并管理一个转向列表"]}, {"name": "dpkg-preconfigure", "trans": ["Debian Linux中软件包安装之前询问问题"]}, {"name": "dpkg-query", "trans": ["Debian Linux中软件包的查询工具"]}, {"name": "dpkg-reconfigure", "trans": ["Debian Linux中重新配制一个已经安装的软件包"]}, {"name": "dpkg-split", "trans": ["Debian Linux中将大软件包分割成小包"]}, {"name": "dpkg-statoverride", "trans": ["Debian Linux中覆盖文件的所有权和模式"]}, {"name": "dpkg-trigger", "trans": ["Debian Linux下的软件包触发器"]}, {"name": "dpkg", "trans": ["Debian Linux系统上安装、创建和管理软件包"]}, {"name": "dris", "trans": ["显示和清空目录堆栈中的内容"]}, {"name": "dstat", "trans": ["通用的系统资源统计工具"]}, {"name": "du", "trans": ["显示每个文件和目录的磁盘使用空间"]}, {"name": "dump", "trans": ["用于备份ext2或者ext3文件系统"]}, {"name": "e2fsck", "trans": ["用于检查第二扩展文件系统的完整性"]}, {"name": "e2label", "trans": ["设置第二扩展文件系统的卷标"]}, {"name": "echo", "trans": ["输出指定的字符串或者变量"]}, {"name": "ed", "trans": ["单行纯文本编辑器"]}, {"name": "edquota", "trans": ["用于编辑指定用户或工作组磁盘配额"]}, {"name": "egrep", "trans": ["在文件内查找指定的字符串"]}, {"name": "eject", "trans": ["用来退出抽取式设备"]}, {"name": "elinks", "trans": ["纯文本界面的WWW浏览器"]}, {"name": "elm", "trans": ["纯文本邮件客户端程序"]}, {"name": "emacs", "trans": ["功能强大的全屏文本编辑器"]}, {"name": "enable", "trans": ["启动或禁用shell内建命令"]}, {"name": "env", "trans": ["显示系统中已存在的环境变量"]}, {"name": "ethtool", "trans": ["显示或修改以太网卡的配置信息"]}, {"name": "ex", "trans": ["启动vim编辑器的ex编辑模式"]}, {"name": "exec", "trans": ["调用并执行指定的命令"]}, {"name": "exit", "trans": ["退出当前的shell。"]}, {"name": "expand", "trans": ["将文件的制表符转换为空白字符"]}, {"name": "export", "trans": ["为shell变量或函数设置导出属性。"]}, {"name": "exportfs", "trans": ["管理NFS共享文件系统列表"]}, {"name": "expr", "trans": ["一款表达式计算工具"]}, {"name": "false", "trans": ["返回状态为失败。"]}, {"name": "fc", "trans": ["显示历史列表中的命令或修改指定的历史命令并执行。"]}, {"name": "fdisk", "trans": ["查看磁盘使用情况和磁盘分区"]}, {"name": "fg", "trans": ["将后台作业移动到前台终端运行"]}, {"name": "fgrep", "trans": ["为文件搜索文字字符串"]}, {"name": "file", "trans": ["用来探测给定文件的类型"]}, {"name": "find", "trans": ["在指定目录下查找文件"]}, {"name": "findfs", "trans": ["标签或UUID查找文件系统"]}, {"name": "finger", "trans": ["用于查找并显示用户信息"]}, {"name": "firewall-cmd", "trans": ["Linux上新用的防火墙软件，跟iptables差不多的工具"]}, {"name": "fishshell", "trans": ["比 bash 更好用的 shell"]}, {"name": "fmt", "trans": ["读取文件后优化处理并输出"]}, {"name": "fold", "trans": ["控制文件内容输出时所占用的屏幕宽度"]}, {"name": "fping", "trans": ["fping检测主机是否存在"]}, {"name": "free", "trans": ["显示内存的使用情况"]}, {"name": "fsck", "trans": ["检查并且试图修复文件系统中的错误"]}, {"name": "ftp", "trans": ["用来设置文件系统相关功能"]}, {"name": "ftpcount", "trans": ["显示目前已FTP登入的用户人数"]}, {"name": "ftpshut", "trans": ["在指定的时间关闭FTP服务器"]}, {"name": "ftptop", "trans": ["proftpd服务器的连接状态"]}, {"name": "ftpwho", "trans": ["显示当前每个ftp会话信息"]}, {"name": "fuser", "trans": ["使用文件或文件结构识别进程"]}, {"name": "gcc", "trans": ["基于C/C++的编译器"]}, {"name": "gcov", "trans": ["测试程序的代码覆盖率的工具"]}, {"name": "gdb", "trans": ["功能强大的程序调试器"]}, {"name": "get_module", "trans": ["获取Linux内核模块的详细信息"]}, {"name": "getenforce", "trans": ["显示当前SELinux的应用模式，是强制、执行还是停用"]}, {"name": "getsebool", "trans": ["查询SElinux策略内各项规则的布尔值"]}, {"name": "git", "trans": ["是目前世界上最先进的分布式版本控制系统"]}, {"name": "gpasswd", "trans": ["Linux下工作组文件的管理工具"]}, {"name": "gpm", "trans": ["提供文字模式下的滑鼠事件处理"]}, {"name": "grep", "trans": ["强大的文本搜索工具"]}, {"name": "groupadd", "trans": ["用于创建一个新的工作组"]}, {"name": "groupdel", "trans": ["用于删除指定的工作组"]}, {"name": "groupmod", "trans": ["更改群组识别码或名称"]}, {"name": "groups", "trans": ["打印指定用户所在组的名称。"]}, {"name": "grpck", "trans": ["用于验证组文件的完整性"]}, {"name": "grpconv", "trans": ["用来开启群组的投影密码"]}, {"name": "grpunconv", "trans": ["用来关闭群组的投影密码"]}, {"name": "grub", "trans": ["多重引导程序grub的命令行shell工具"]}, {"name": "gunzip", "trans": ["用来解压缩文件"]}, {"name": "gzexe", "trans": ["用来压缩可执行文件"]}, {"name": "gzip", "trans": ["用来压缩文件"]}, {"name": "halt", "trans": ["关闭正在运行的Linux操作系统"]}, {"name": "hdparm", "trans": ["显示与设定硬盘的参数"]}, {"name": "head", "trans": ["显示文件的开头部分。"]}, {"name": "help", "trans": ["该命令是bash内建命令，用于显示bash内建命令的帮助信息。"]}, {"name": "hexdump", "trans": ["显示文件十六进制格式"]}, {"name": "history", "trans": ["显示或操作历史列表。"]}, {"name": "host", "trans": ["常用的分析域名查询工具"]}, {"name": "hostid", "trans": ["显示当前主机的十六进制数字标识。"]}, {"name": "hostname", "trans": ["显示和设置系统的主机名"]}, {"name": "hostnamectl", "trans": ["查询或更改系统主机名"]}, {"name": "hping3", "trans": ["测试网络及主机的安全"]}, {"name": "htdigest", "trans": ["Apache服务器内置工具"]}, {"name": "htop", "trans": ["[非内部命令]一个互动的进程查看器，可以动态观察系统进程状况"]}, {"name": "htpasswd", "trans": ["apache服务器创建密码认证文件"]}, {"name": "hwclock", "trans": ["显示与设定硬件时钟"]}, {"name": "iconv", "trans": ["转换文件的编码方式"]}, {"name": "id", "trans": ["打印真实以及有效的用户和所在组的信息"]}, {"name": "ifcfg", "trans": ["置Linux中的网络接口参数"]}, {"name": "ifconfig", "trans": ["配置和显示Linux系统网卡的网络参数"]}, {"name": "ifdown", "trans": ["禁用指定的网络接口"]}, {"name": "ifstat", "trans": ["统计网络接口流量状态"]}, {"name": "iftop", "trans": ["一款实时流量监控工具"]}, {"name": "ifup", "trans": ["激活指定的网络接口"]}, {"name": "indent", "trans": ["格式化C语言的源文件"]}, {"name": "info", "trans": ["Linux下info格式的帮助指令"]}, {"name": "init", "trans": ["init进程是所有Linux进程的父进程"]}, {"name": "inotifywait", "trans": ["异步文件系统监控机制"]}, {"name": "insmod", "trans": ["将给定的模块加载到内核中"]}, {"name": "install", "trans": ["安装或升级软件或备份数据"]}, {"name": "iostat", "trans": ["监视系统输入输出设备和CPU的使用情况"]}, {"name": "iotop", "trans": ["用来监视磁盘I/O使用状况的工具"]}, {"name": "ip", "trans": ["网络配置工具"]}, {"name": "ip6tables-restore", "trans": ["还原ip6tables表"]}, {"name": "ip6tables-save", "trans": ["保存ip6tables表配置"]}, {"name": "ip6tables", "trans": ["linux中防火墙软件"]}, {"name": "ipcalc", "trans": ["简单的IP地址计算器"]}, {"name": "ipcrm", "trans": ["删除消息队列、信号集、或者共享内存标识"]}, {"name": "ipcs", "trans": ["分析消息队列共享内存和信号量 "]}, {"name": "iperf", "trans": ["网络性能测试工具"]}, {"name": "iptables-restore", "trans": ["还原iptables表的配置"]}, {"name": "iptables-save", "trans": ["备份iptables的表配置"]}, {"name": "iptables", "trans": ["Linux上常用的防火墙软件"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["实时地监视网卡流量"]}, {"name": "iptstate", "trans": ["显示iptables的工作状态"]}, {"name": "ispell", "trans": ["检查文件中出现的拼写错误"]}, {"name": "jed", "trans": ["主要用于编辑代码的编辑器"]}, {"name": "jobs", "trans": ["显示作业的状态。"]}, {"name": "joe", "trans": ["强大的纯文本编辑器"]}, {"name": "join", "trans": ["两个文件中指定栏位内容相同的行连接起来"]}, {"name": "jq", "trans": ["一个灵活的轻量级命令行JSON处理器"]}, {"name": "jwhois", "trans": ["whois 客户端服务"]}, {"name": "kernelversion", "trans": ["打印当前内核的主版本号"]}, {"name": "kexec", "trans": ["从当前正在运行的内核引导到一个新内核"]}, {"name": "kill", "trans": ["发送信号到进程。"]}, {"name": "killall", "trans": ["使用进程的名称来杀死一组进程"]}, {"name": "last", "trans": ["列出目前与过去登入系统的用户相关信息"]}, {"name": "lastb", "trans": ["列出登入系统失败的用户相关信息"]}, {"name": "lastlog", "trans": ["显示系统中所有用户最近一次登录信息"]}, {"name": "ld", "trans": ["将目标文件连接为可执行程序"]}, {"name": "ldconfig", "trans": ["动态链接库管理命令"]}, {"name": "ldd", "trans": ["打印程序或者库文件所依赖的共享库列表"]}, {"name": "less", "trans": ["分屏上下翻页浏览文件内容"]}, {"name": "let", "trans": ["简单的计算器，执行算术表达式。"]}, {"name": "lftp", "trans": ["优秀的文件客户端程序"]}, {"name": "lftpget", "trans": ["调用lftp指令下载指定的文件"]}, {"name": "lha", "trans": ["压缩或解压缩lzh格式文件"]}, {"name": "lilo", "trans": ["安装核心载入开机管理程序"]}, {"name": "ln", "trans": ["用来为文件创建链接"]}, {"name": "lnstat", "trans": ["显示Linux系统的网路状态"]}, {"name": "local", "trans": ["在函数内定义局部变量。"]}, {"name": "locate", "trans": ["比 find 好用的文件查找工具"]}, {"name": "logger", "trans": ["在系统日志中记录相应条目"]}, {"name": "login", "trans": ["登录系统或切换用户身份"]}, {"name": "logname", "trans": ["打印当前终端登录用户的名称。"]}, {"name": "logout", "trans": ["退出当前登录的Shell"]}, {"name": "logrotate", "trans": ["系统日志进行轮转、压缩和删除"]}, {"name": "logsave", "trans": ["将命令的输出信息保存到指定的日志文件"]}, {"name": "logwatch", "trans": ["可定制和可插入式的日志监视系统"]}, {"name": "look", "trans": ["显示文件中以指定字符串开头的任意行"]}, {"name": "losetup", "trans": ["设定与控制循环（loop）设备"]}, {"name": "lp", "trans": ["打印文件或修改排队的打印任务"]}, {"name": "l<PERSON>min", "trans": ["配置CUPS套件中的打印机和类"]}, {"name": "lpc", "trans": ["命令行方式打印机控制程序"]}, {"name": "lpq", "trans": ["显示打印队列中的打印任务的状态信息"]}, {"name": "lpr", "trans": ["将文件发送给指定打印机进行打印"]}, {"name": "lprm", "trans": ["删除打印队列中的打印任务"]}, {"name": "lpstat", "trans": ["显示CUPS中打印机的状态信息"]}, {"name": "ls", "trans": ["显示目录内容列表"]}, {"name": "lsattr", "trans": ["查看文件的第二扩展文件系统属性"]}, {"name": "lsb_release", "trans": ["显示发行版本信息"]}, {"name": "lsblk", "trans": ["列出块设备信息"]}, {"name": "lscpu", "trans": ["显示有关CPU架构的信息"]}, {"name": "lsmod", "trans": ["显示已载入系统的模块"]}, {"name": "lsof", "trans": ["显示Linux系统当前已打开的所有文件列表 `lsof -p pid`"]}, {"name": "lspci", "trans": ["显示当前主机的所有PCI总线信息"]}, {"name": "lsusb", "trans": ["显示本机的USB设备列表信息"]}, {"name": "ltrace", "trans": ["用来跟踪进程调用库函数的情况"]}, {"name": "lvcreate", "trans": ["用于创建LVM的逻辑卷"]}, {"name": "lvdisplay", "trans": ["显示逻辑卷属性"]}, {"name": "lvextend", "trans": ["扩展逻辑卷空间"]}, {"name": "lvreduce", "trans": ["收缩逻辑卷空间"]}, {"name": "lvremove", "trans": ["删除指定LVM逻辑卷"]}, {"name": "l<PERSON><PERSON>", "trans": ["调整逻辑卷空间大小"]}, {"name": "lvscan", "trans": ["扫描逻辑卷"]}, {"name": "lynx", "trans": ["纯文本模式的网页浏览器"]}, {"name": "mail", "trans": ["命令行下发送和接收电子邮件"]}, {"name": "mailq", "trans": ["显示待发送的邮件队列"]}, {"name": "mailstat", "trans": ["显示到达的邮件状态"]}, {"name": "make", "trans": ["GNU的工程化编译工具"]}, {"name": "man", "trans": ["查看Linux中的指令帮助"]}, {"name": "mapfile", "trans": ["从标准输入读取行并赋值到数组。"]}, {"name": "md5sum", "trans": ["计算和校验文件报文摘要的工具程序"]}, {"name": "mesg", "trans": ["设置当前终端的写权限"]}, {"name": "mii-tool", "trans": ["配置网络设备协商方式的工具"]}, {"name": "mkbootdisk", "trans": ["可建立目前系统的启动盘"]}, {"name": "mkdir", "trans": ["用来创建目录"]}, {"name": "mke2fs", "trans": ["创建磁盘分区上的“etc2/etc3”文件系统"]}, {"name": "mkfs", "trans": ["用于在设备上创建Linux文件系统"]}, {"name": "m<PERSON><PERSON>d", "trans": ["建立要载入ramdisk的映像文件"]}, {"name": "mkisofs", "trans": ["建立ISO 9660映像文件"]}, {"name": "mknod", "trans": ["创建字符设备文件和块设备文件"]}, {"name": "mkswap", "trans": ["建立和设置SWAP交换分区"]}, {"name": "mktemp", "trans": ["创建临时文件供shell脚本使用"]}, {"name": "modprobe", "trans": ["自动处理可载入模块"]}, {"name": "more", "trans": ["显示文件内容，每次显示一屏"]}, {"name": "mount", "trans": ["用于挂载Linux系统外的文件"]}, {"name": "mpstat", "trans": ["显示各个可用CPU的状态"]}, {"name": "mtools", "trans": ["显示mtools支持的指令"]}, {"name": "mv", "trans": ["用来对文件或目录重新命名"]}, {"name": "mysql", "trans": ["MySQL服务器客户端工具"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trans": ["MySQL服务器管理客户端"]}, {"name": "mysqldump", "trans": ["MySQL数据库中备份工具"]}, {"name": "mysqlimport", "trans": ["为MySQL服务器用命令行方式导入数据"]}, {"name": "mysqlshow", "trans": ["显示MySQL中数据库相关信息"]}, {"name": "named-checkzone", "trans": ["使用named-checkzone命令可以进行区域文件有效性检查和转换，必须指定区域名称和区域文件名称"]}, {"name": "nano", "trans": ["字符终端文本编辑器"]}, {"name": "nc", "trans": ["用于设置路由器，是网络工具中的瑞士军刀"]}, {"name": "ncftp", "trans": ["是增强的的FTP工具"]}, {"name": "nethogs", "trans": ["终端下的网络流量监控工具"]}, {"name": "netstat", "trans": ["查看Linux中网络系统状态信息"]}, {"name": "newusers", "trans": ["用于批处理的方式一次创建多个命令"]}, {"name": "nfsstat", "trans": ["列出NFS客户端和服务器的工作状态"]}, {"name": "ngrep", "trans": ["方便的数据包匹配和显示工具"]}, {"name": "nice", "trans": ["改变程序执行的优先权等级"]}, {"name": "nisdomainname", "trans": ["显示主机NIS的域名"]}, {"name": "nl", "trans": ["为每一个文件添加行号。"]}, {"name": "nm", "trans": ["显示二进制目标文件的符号表"]}, {"name": "nmap", "trans": ["网络探测和安全审核"]}, {"name": "nmcli", "trans": ["地址配置工具"]}, {"name": "nohup", "trans": ["将程序以忽略挂起信号的方式运行起来"]}, {"name": "nologin", "trans": ["拒绝用户登录系统"]}, {"name": "nproc", "trans": ["打印可用的处理器单元数量。"]}, {"name": "nslookup", "trans": ["查询域名DNS信息的工具"]}, {"name": "ntpdate", "trans": ["使用网络计时协议（NTP）设置日期和时间"]}, {"name": "ntsysv", "trans": ["集中管理系统的各种服务"]}, {"name": "objdu<PERSON>", "trans": ["显示二进制文件信息"]}, {"name": "od", "trans": ["输出文件的八进制、十六进制等格式编码的字节"]}, {"name": "openssl", "trans": ["强大的安全套接字层密码库"]}, {"name": "parted", "trans": ["磁盘分区和分区大小调整工具"]}, {"name": "partprobe", "trans": ["不重启的情况下重读分区"]}, {"name": "passwd", "trans": ["用于让用户可以更改自己的密码"]}, {"name": "paste", "trans": ["将多个文件按列队列合并"]}, {"name": "patch", "trans": ["为开放源代码软件安装补丁程序"]}, {"name": "pathchk", "trans": ["检查文件中不可移植的部分"]}, {"name": "perl", "trans": ["perl语言解释器"]}, {"name": "pfctl", "trans": ["PF防火墙的配置命令"]}, {"name": "pgrep", "trans": ["根据用户给出的信息在当前运行进程中查找并列出符合条件的进程ID（PID）"]}, {"name": "php", "trans": ["PHP语言的命令行接口"]}, {"name": "pico", "trans": ["功能强大全屏幕的文本编辑器"]}, {"name": "pidof", "trans": ["查找指定名称的进程的进程号ID号"]}, {"name": "pigz", "trans": ["可以用来解压缩文件，gzip的并行实现升级版"]}, {"name": "ping", "trans": ["测试主机之间网络的连通性"]}, {"name": "pkill", "trans": ["可以按照进程名杀死进程"]}, {"name": "pmap", "trans": ["报告进程的内存映射关系"]}, {"name": "popd", "trans": ["从目录堆栈中删除目录。"]}, {"name": "poweroff", "trans": ["关闭Linux系统，关闭记录会被写入到/var/log/wtmp日志文件中"]}, {"name": "ppp-off", "trans": ["关闭ppp连线"]}, {"name": "pr", "trans": ["将文本文件转换成适合打印的格式"]}, {"name": "printf", "trans": ["格式化并输出结果。"]}, {"name": "protoize", "trans": ["GNU-C代码转换为ANSI-C代码"]}, {"name": "ps", "trans": ["报告当前系统的进程状态"]}, {"name": "pssh", "trans": ["批量管理执行"]}, {"name": "pstack", "trans": ["显示每个进程的栈跟踪"]}, {"name": "pstree", "trans": ["以树状图的方式展现进程之间的派生关系"]}, {"name": "pushd", "trans": ["将目录添加到目录堆栈顶部。"]}, {"name": "pv", "trans": ["显示当前在命令行执行的命令的进度信息，管道查看器"]}, {"name": "pvchange", "trans": ["修改物理卷属性"]}, {"name": "pvck", "trans": ["检测物理卷的LVM元数据的一致性"]}, {"name": "pvcreate", "trans": ["将物理硬盘分区初始化为物理卷"]}, {"name": "pvdisplay", "trans": ["显示物理卷的属性"]}, {"name": "pvremove", "trans": ["删除一个存在的物理卷"]}, {"name": "pvs", "trans": ["输出物理卷信息报表"]}, {"name": "pvscan", "trans": ["扫描系统中所有硬盘的物理卷列表"]}, {"name": "pwck", "trans": ["用来验证系统认证文件内容和格式的完整性"]}, {"name": "pwconv", "trans": ["用来开启用户的投影密码"]}, {"name": "pwd", "trans": ["显示当前工作目录。"]}, {"name": "pwunconv", "trans": ["用来关闭用户的投影密码"]}, {"name": "quota", "trans": ["显示磁盘已使用的空间与限制"]}, {"name": "quotacheck", "trans": ["检查磁盘的使用空间与限制"]}, {"name": "quotaoff", "trans": ["关闭Linux内核中指定文件系统的磁盘配额功能"]}, {"name": "quotaon", "trans": ["激活Linux内核中指定文件系统的磁盘配额功能"]}, {"name": "rcconf", "trans": ["Debian Linux下的运行等级服务配置工具"]}, {"name": "rcp", "trans": ["使在两台Linux主机之间的文件复制操作更简单"]}, {"name": "read", "trans": ["从键盘读取变量值"]}, {"name": "readelf", "trans": ["用于显示elf格式文件的信息"]}, {"name": "readonly", "trans": ["标记shell变量或函数为只读"]}, {"name": "reboot", "trans": ["重新启动正在运行的Linux操作系统"]}, {"name": "reject", "trans": ["指示打印系统拒绝发往指定目标打印机的打印任务"]}, {"name": "rename", "trans": ["用字符串替换的方式批量改变文件名"]}, {"name": "renice", "trans": ["修改正在运行的进程的调度优先级"]}, {"name": "repquota", "trans": ["报表的格式输出磁盘空间限制的状态"]}, {"name": "resize", "trans": ["命令设置终端机视窗的大小"]}, {"name": "restore", "trans": ["所进行的操作和dump指令相反"]}, {"name": "restorecon", "trans": ["恢复文件的安全上下文"]}, {"name": "return", "trans": ["从函数中退出并返回数值。"]}, {"name": "rev", "trans": ["将文件内容以字符为单位反序输出"]}, {"name": "rexec", "trans": ["远程执行Linux系统下命令"]}, {"name": "rlogin", "trans": ["从当前终端登录到远程Linux主机"]}, {"name": "rm", "trans": ["用于删除给定的文件和目录"]}, {"name": "rmdir", "trans": ["用来删除空目录"]}, {"name": "rmmod", "trans": ["从运行的内核中移除指定的内核模块"]}, {"name": "route", "trans": ["显示并设置Linux中静态路由表"]}, {"name": "rpm", "trans": ["RPM软件包的管理工具"]}, {"name": "rpm2cpio", "trans": ["将RPM软件包转换为cpio格式的文件"]}, {"name": "rpmbuild", "trans": ["创建RPM的二进制软件包和源码软件包"]}, {"name": "rpmdb", "trans": ["初始化和重建RPM数据库"]}, {"name": "rpmquery", "trans": ["从RPM数据库中查询软件包信息"]}, {"name": "rpmsign", "trans": ["使用RPM软件包的签名管理工具"]}, {"name": "rpmverify", "trans": ["验证已安装的RPM软件包的正确性"]}, {"name": "rsh", "trans": ["连接远程主机并执行命令"]}, {"name": "rsync", "trans": ["远程数据同步工具"]}, {"name": "runlevel", "trans": ["打印当前Linux系统的运行等级"]}, {"name": "sar", "trans": ["系统运行状态统计工具"]}, {"name": "scp", "trans": ["加密的方式在本地主机和远程主机之间复制文件"]}, {"name": "screen", "trans": ["用于命令行终端切换"]}, {"name": "script", "trans": ["记录终端会话的所有操作"]}, {"name": "scriptreplay", "trans": ["重新播放终端会话的所有操作"]}, {"name": "sed", "trans": ["功能强大的流式文本编辑器"]}, {"name": "seinfo", "trans": ["查询SELinux的策略提供多少相关规则"]}, {"name": "semanage", "trans": ["默认目录的安全上下文查询与修改"]}, {"name": "sendmail", "trans": ["著名电子邮件服务器"]}, {"name": "seq", "trans": ["以指定增量从首数开始打印数字到尾数"]}, {"name": "service", "trans": ["控制系统服务的实用工具"]}, {"name": "sesearch", "trans": ["查询SELinux策略的规则详情"]}, {"name": "set", "trans": ["显示或设置shell特性及shell变量"]}, {"name": "setfacl", "trans": ["设置文件访问控制列表"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["查询和配置PCI设备的使用工具"]}, {"name": "setsebool", "trans": ["修改SElinux策略内各项规则的布尔值"]}, {"name": "setsid", "trans": ["在新的会话中运行程序"]}, {"name": "sftp-server", "trans": ["sftp协议的服务器端程序"]}, {"name": "sftp", "trans": ["交互式的文件传输程序"]}, {"name": "sh", "trans": ["shell命令解释器"]}, {"name": "shift", "trans": ["移动位置参数。"]}, {"name": "shopt", "trans": ["显示和设置shell操作选项"]}, {"name": "showmount", "trans": ["显示NFS服务器加载的信息"]}, {"name": "shuf", "trans": ["产生随机的排列。"]}, {"name": "shutdown", "trans": ["用来执行系统关机的命令"]}, {"name": "skill", "trans": ["向选定的进程发送信号冻结进程"]}, {"name": "slabtop", "trans": ["实时显示内核slab内存缓存信息"]}, {"name": "sleep", "trans": ["将目前动作延迟一段时间"]}, {"name": "slocate", "trans": ["命令查找文件或目录"]}, {"name": "smbclient", "trans": ["交互方式访问samba服务器"]}, {"name": "smbpasswd", "trans": ["samba用户和密码管理工具"]}, {"name": "sort", "trans": ["对文本文件中所有行进行排序。"]}, {"name": "source", "trans": ["在当前Shell环境中从指定文件读取和执行命令。"]}, {"name": "speedtest-cli", "trans": ["命令行下测试服务器外网速度"]}, {"name": "spell", "trans": ["对文件进行拼写检查"]}, {"name": "split", "trans": ["分割任意大小的文件"]}, {"name": "squid", "trans": ["squid服务器守护进程"]}, {"name": "squidclient", "trans": ["squid服务器的客户端管理工具"]}, {"name": "ss", "trans": ["比 netstat 好用的socket统计信息，iproute2 包附带的另一个工具，允许你查询 socket 的有关统计信息"]}, {"name": "ssh-add", "trans": ["把专用密钥添加到ssh-agent的高速缓存中"]}, {"name": "ssh-agent", "trans": ["ssh密钥管理器"]}, {"name": "ssh-copy-id", "trans": ["把本地的ssh公钥文件安装到远程主机对应的账户下"]}, {"name": "ssh-keygen", "trans": ["为ssh生成、管理和转换认证密钥"]}, {"name": "ssh-keyscan", "trans": ["收集主机公钥的使用工具"]}, {"name": "ssh", "trans": ["openssh套件中的客户端连接工具"]}, {"name": "sshd", "trans": ["openssh软件套件中的服务器守护进程"]}, {"name": "startx", "trans": ["用来启动X Window"]}, {"name": "stat", "trans": ["用于显示文件的状态信息"]}, {"name": "strace", "trans": ["跟踪系统调用和信号"]}, {"name": "strings", "trans": ["在对象文件或二进制文件中查找可打印的字符串"]}, {"name": "stty", "trans": ["修改终端命令行的相关设置"]}, {"name": "su", "trans": ["用于切换当前用户身份到其他用户身份"]}, {"name": "sudo", "trans": ["以其他身份来执行命令"]}, {"name": "sum", "trans": ["计算文件的校验码和显示块数"]}, {"name": "supervisord", "trans": ["配置后台服务/常驻进程的进程管家工具"]}, {"name": "suspend", "trans": ["挂起shell的执行。"]}, {"name": "swapoff", "trans": ["关闭指定的交换空间"]}, {"name": "swapon", "trans": ["激活Linux系统中交换空间"]}, {"name": "sync", "trans": ["用于强制被改变的内容立刻写入磁盘"]}, {"name": "sysctl", "trans": ["时动态地修改内核的运行参数"]}, {"name": "syslog", "trans": ["系统默认的日志守护进程"]}, {"name": "systemctl", "trans": ["系统服务管理器指令"]}, {"name": "systool", "trans": ["显示基于总线、类和拓扑显示系统中设备的信息"]}, {"name": "tac", "trans": ["连接多个文件并以行为单位反向打印到标准输出。"]}, {"name": "tail", "trans": ["在屏幕上显示指定文件的末尾若干行"]}, {"name": "tailf", "trans": ["在屏幕上显示指定文件的末尾若干行内容，通常用于日志文件的跟踪输出"]}, {"name": "talk", "trans": ["让用户和其他用户聊天"]}, {"name": "tar", "trans": ["将许多文件一起保存至一个单独的磁带或磁盘归档，并能从归档中单独还原所需文件。"]}, {"name": "tcpdump", "trans": ["一款sniffer工具，是Linux上的抓包工具，嗅探器"]}, {"name": "tcpreplay", "trans": ["将PCAP包重新发送，用于性能或者功能测试"]}, {"name": "tee", "trans": ["从标准输入读取数据并重定向到标准输出和文件。"]}, {"name": "telint", "trans": ["切换当前正在运行系统的运行等级"]}, {"name": "telnet", "trans": ["登录远程主机和管理(测试ip端口是否连通)"]}, {"name": "tempfile", "trans": ["shell中给临时文件命名"]}, {"name": "test", "trans": ["执行条件表达式。"]}, {"name": "tftp", "trans": ["在本机和tftp服务器之间使用TFTP协议传输文件"]}, {"name": "time", "trans": ["统计给定命令所花费的总时间"]}, {"name": "times", "trans": ["显示进程累计时间。"]}, {"name": "tload", "trans": ["显示系统负载状况"]}, {"name": "tmux", "trans": ["Tmux是一个优秀的终端复用软件，类似GNU Screen，但来自于OpenBSD，采用BSD授权"]}, {"name": "top", "trans": ["显示或管理执行中的程序"]}, {"name": "touch", "trans": ["创建新的空文件"]}, {"name": "tput", "trans": ["通过terminfo数据库对终端会话进行初始化和操作"]}, {"name": "tr", "trans": ["将字符进行替换压缩和删除"]}, {"name": "tracepath", "trans": ["追踪目的主机经过的路由信息"]}, {"name": "traceroute", "trans": ["显示数据包到主机间的路径"]}, {"name": "trap", "trans": ["捕捉信号和其他事件并执行命令。"]}, {"name": "tree", "trans": ["树状图列出目录的内容"]}, {"name": "true", "trans": ["返回状态为成功。"]}, {"name": "tty", "trans": ["显示连接到当前标准输入的终端设备文件名"]}, {"name": "type", "trans": ["显示指定命令的类型。"]}, {"name": "ulimit", "trans": ["控制shell程序的资源"]}, {"name": "umask", "trans": ["显示或设置创建文件的权限掩码。"]}, {"name": "umount", "trans": ["用于卸载已经加载的文件系统"]}, {"name": "unalias", "trans": ["删除由alias设置的别名"]}, {"name": "uname", "trans": ["打印系统信息。"]}, {"name": "unarj", "trans": ["解压缩由arj命令创建的压缩包"]}, {"name": "uncompress", "trans": ["用来解压.Z文件"]}, {"name": "unexpand", "trans": ["将文件的空白字符转换为制表符"]}, {"name": "uniq", "trans": ["显示或忽略重复的行。"]}, {"name": "unlink", "trans": ["系统调用函数unlink去删除指定的文件"]}, {"name": "unprotoize", "trans": ["删除C语言源代码文件中的函数原型"]}, {"name": "unrar", "trans": ["解压rar文件命令，从rar档案中提取文件"]}, {"name": "unset", "trans": ["删除指定的shell变量或函数。"]}, {"name": "unzip", "trans": ["用于解压缩由zip命令压缩的压缩包"]}, {"name": "updatedb", "trans": ["创建或更新slocate命令所必需的数据库文件"]}, {"name": "uptime", "trans": ["查看Linux系统负载信息"]}, {"name": "useradd", "trans": ["创建的新的系统用户"]}, {"name": "userdel", "trans": ["用于删除给定的用户以及与用户相关的文件"]}, {"name": "usermod", "trans": ["用于修改用户的基本信息"]}, {"name": "usernetctl", "trans": ["被允许时操作指定的网络接口"]}, {"name": "users", "trans": ["打印当前主机所有登陆用户的名称。"]}, {"name": "uucico", "trans": ["UUCP文件传输服务程序"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["命令处理传送进来的文件"]}, {"name": "u<PERSON>", "trans": ["将文件传送到远端的UUCP主机"]}, {"name": "vdfuse", "trans": ["VirtualBox软件挂载VDI分区文件工具"]}, {"name": "vgchange", "trans": ["修改卷组属性"]}, {"name": "vgconvert", "trans": ["转换卷组元数据格式"]}, {"name": "vgcreate", "trans": ["用于创建LVM卷组"]}, {"name": "vgdisplay", "trans": ["显示LVM卷组的信息"]}, {"name": "vgextend", "trans": ["向卷组中添加物理卷"]}, {"name": "vgreduce", "trans": ["从卷组中删除物理卷"]}, {"name": "vgremove", "trans": ["用于用户删除LVM卷组"]}, {"name": "vgrename", "trans": ["使用vgrename命令可以重命名卷组的名称"]}, {"name": "vgscan", "trans": ["扫描并显示系统中的卷组"]}, {"name": "vi", "trans": ["功能强大的纯文本编辑器"]}, {"name": "vmstat", "trans": ["显示虚拟内存状态"]}, {"name": "volname", "trans": ["显示指定的ISO-9660格式的设备的卷名称"]}, {"name": "w", "trans": ["显示目前登入系统的用户信息"]}, {"name": "wait", "trans": ["等待进程执行完后返回"]}, {"name": "wall", "trans": ["向系统当前所有打开的终端上输出信息"]}, {"name": "watch", "trans": ["可以将命令的输出结果输出到标准输出设备，多用于周期性执行命令/定时执行命令"]}, {"name": "wc", "trans": ["统计文件的字节数、字数、行数"]}, {"name": "wget", "trans": ["Linux系统下载文件工具"]}, {"name": "whatis", "trans": ["查询一个命令执行什么功能"]}, {"name": "whereis", "trans": ["查找二进制程序、代码等相关文件路径"]}, {"name": "which", "trans": ["查找并显示给定命令的绝对路径"]}, {"name": "who", "trans": ["显示当前所有登陆用户的信息。"]}, {"name": "whoami", "trans": ["打印当前有效的用户ID对应的名称"]}, {"name": "write", "trans": ["向指定登录用户终端上发送信息"]}, {"name": "xargs", "trans": ["给其他命令传递参数的一个过滤器"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["显示和编辑被用于连接X服务器的认证信息"]}, {"name": "xclip", "trans": ["管理 X 粘贴板"]}, {"name": "xhost", "trans": ["制哪些X客户端能够在X服务器上显示"]}, {"name": "xinit", "trans": ["是Linux下X-Window系统的初始化程序"]}, {"name": "xlsatoms", "trans": ["列出X服务器内部所有定义的原子成分"]}, {"name": "xlsclients", "trans": ["列出显示器中的客户端应用程序"]}, {"name": "xlsfonts", "trans": ["列出X Server使用的字体"]}, {"name": "xset", "trans": ["设置X-Window系统中的用户爱好的实用工具"]}, {"name": "xz", "trans": ["POSIX 平台开发具有高压缩率的工具"]}, {"name": "yes", "trans": ["重复打印指定字符串"]}, {"name": "ypdomainname", "trans": ["显示主机的NIS的域名"]}, {"name": "yum", "trans": ["基于RPM的软件包管理器"]}, {"name": "zcat", "trans": ["显示压缩包中文件的内容"]}, {"name": "zfore", "trans": ["强制为gzip格式的压缩文件添加.gz后缀"]}, {"name": "zip", "trans": ["可以用来解压缩文件"]}, {"name": "zipinfo", "trans": ["用来列出压缩文件信息"]}, {"name": "zipsplit", "trans": ["将较大的zip压缩包分割成各个较小的压缩包"]}, {"name": "znew", "trans": ["将.Z压缩包重新转化为gzip命令压缩的.gz压缩包"]}]