[{"name": "pets not allowed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "take property insurance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blackout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emergency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire gate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elevator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twin room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stereo system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "water heater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vacuum cleaner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coffee maker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refrigerator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fridge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bed linen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "real estate agency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "property agency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landlord", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landlady", "trans": [], "usphone": "", "ukphone": ""}, {"name": "on lease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "for rent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deposit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utilities", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incoming phone is free", "trans": [], "usphone": "", "ukphone": ""}, {"name": "noisy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laundry room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "burglar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electric cooker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gas cooker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cooker hood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cassette recorder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire place", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electric heater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surroundings", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rural areas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suburb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "downtown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "semi-detached house", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detached house", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terraced houses", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apartment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dormitory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dorm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "student hotel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "youth hostel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "furnished", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unfurnished", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entrance hall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lobby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "porch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "balcony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bedsit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bedroom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "living room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lounge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "single room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "double bedroom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bathroom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kitchen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shared kitchen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carpet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pillow case", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sheet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mattress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spread", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pillow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cushion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blanket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "towel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coffee table", "trans": [], "usphone": "", "ukphone": ""}, {"name": "armchair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sofa", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kettle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cupboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bath", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toilet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stove", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dishwasher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freezer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "telephone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "washing machine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "air-conditioner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "central air-conditioning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "central heating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electric fans", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microwave oven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electric stoves", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radiator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rental price", "trans": [], "usphone": "", "ukphone": ""}, {"name": "no privacy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "partially furnished", "trans": [], "usphone": "", "ukphone": ""}, {"name": "residential number", "trans": [], "usphone": "", "ukphone": ""}, {"name": "available", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mobile phone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cellular phone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cordless phone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "satellite TV", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cable TV", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antenna", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aerial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "closed circuit TV", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lobby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lift lobby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cellar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "client", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tenant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "home stay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "host family", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shared accommodation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "university hall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hall of residence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accommodation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maximum rent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minimum rent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decorate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ornament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shutter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blinder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curtain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utensil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scuba-diving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snorkeling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glass-bottom boat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "water skiing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "white water rafting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunrise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jellyfish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sun block", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sun screen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sun burnt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bathers", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trunks", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunglass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beach towel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "picnic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expedition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excursion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "get lost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "helmet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "torch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flash light", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mosquito net", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repellent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hiking boots", "trans": [], "usphone": "", "ukphone": ""}, {"name": "socks", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irritation plants", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bungee jumping", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rock climbing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hang gliding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parachuting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "castle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ancient temple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cathedral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aquarium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "botanical garden", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amusement park", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organic farm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valley", "trans": [], "usphone": "", "ukphone": ""}, {"name": "waterfall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hot spring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bush walking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "voyage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "package tour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "off-road driving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "four-wheel drive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "caravan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "express train", "trans": [], "usphone": "", "ukphone": ""}, {"name": "economy class", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cottage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "B & B", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cabin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "itinerary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Great Barrier Reef", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Sydney Opera House", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Stonehenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go camping", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wild-life zoo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "national park", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charter flight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "domestic animal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sleeping bag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recreation office", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brochure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mountain bike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pannier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backpack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "directory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snorer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reservation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "schedule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vacant seat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extra charge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heritage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commence the tour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handicapped", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wheel chair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhibition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reproduction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sculpture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diving gear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rain forest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leisure activity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vineyard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lodge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visa extension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "non-open time", "trans": [], "usphone": "", "ukphone": ""}, {"name": "travel agency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flight number", "trans": [], "usphone": "", "ukphone": ""}, {"name": "book the ticket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit card", "trans": [], "usphone": "", "ukphone": ""}, {"name": "passport", "trans": [], "usphone": "", "ukphone": ""}, {"name": "driving license", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "check in", "trans": [], "usphone": "", "ukphone": ""}, {"name": "take off", "trans": [], "usphone": "", "ukphone": ""}, {"name": "land", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hostel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "museum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "souvenir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "art gallery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hiking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hitch-hike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surfing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skiing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bag-packer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "platform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calories", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calcium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sodium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vitamin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protein", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dietary fiber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nutrient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cereal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amino acid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "metabolism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indigestion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tissue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "healthy diet pyramid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tennis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "table tennis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "water polo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skiing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "water skiing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gym", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yoga", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cycling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jogging", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weight training", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aerobics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stadium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hi-tech fitness center", "trans": [], "usphone": "", "ukphone": ""}, {"name": "squash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cricket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rugby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "golf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bowling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "billiard house", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snooker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hockey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ice hockey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "roller blade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "badminton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "baseball", "trans": [], "usphone": "", "ukphone": ""}, {"name": "softball", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beach volleyball", "trans": [], "usphone": "", "ukphone": ""}, {"name": "football", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treadmill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "track and field", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tournament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "athlete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "referee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheer leader", "trans": [], "usphone": "", "ukphone": ""}, {"name": "martial art", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrestling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "judo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "karate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "box", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canoeing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lung infection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drowsiness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Yellow Fever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vaccine injection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chemist’s", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pharmacy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drug store", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optic examination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allergy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leprosy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "epidemic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chronic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precaution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "influenza", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ulcer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clinic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hospital", "trans": [], "usphone": "", "ukphone": ""}, {"name": "first aid kit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "first aid station", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dentist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dizzy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "headache", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pulse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blood pressure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "symptom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sore throat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stuffed nose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "running nose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surgeon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prescription", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penicillin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aspirin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antibiotics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "life expectancy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hay fever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tablet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "herb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capsule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ointment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lozenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syrup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "food poison", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "check up", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insomnia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arthritis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fatigue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sprain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bruise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sneeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hiccup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diarrhea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "therapy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diagnosis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dosage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "currency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "note", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheque", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chequebook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traveler’s cheque", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit card", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Master", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Visa", "trans": [], "usphone": "", "ukphone": ""}, {"name": "American Express", "trans": [], "usphone": "", "ukphone": ""}, {"name": "account", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pin number", "trans": [], "usphone": "", "ukphone": ""}, {"name": "activate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deposit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "withdraw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bank statement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interest rate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ATM", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overdraft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mortgage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "net", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gross", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bonus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "branch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eligible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "individual circumstances", "trans": [], "usphone": "", "ukphone": ""}, {"name": "documentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "special concession", "trans": [], "usphone": "", "ukphone": ""}, {"name": "money order", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transfer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "common room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canteen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dining hall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refectory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "buffet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barbecue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snack bar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "candy bar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vending machine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spicy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chilli", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sweet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bitter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "no cheese product", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garden salad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fish & chips", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refreshment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pork", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sandwich", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appetizer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "main course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dessert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beverage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vegetarian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sea food", "trans": [], "usphone": "", "ukphone": ""}, {"name": "take-away", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drive-in", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pub", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instant coffee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "espresso", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cappuccino", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brewed coffee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mineral water", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "black tea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skimmed milk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yogurt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ginger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vinegar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aubergine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "egg plant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "broccoli", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cauliflower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "celery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spinach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cucumber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mushroom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pumpkin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lettuce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "asparagus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lentil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attendance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exemption", "trans": [], "usphone": "", "ukphone": ""}, {"name": "re-sit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "report", "trans": [], "usphone": "", "ukphone": ""}, {"name": "set exercises", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bibliography", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high distinction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distinction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Arts", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Science", "trans": [], "usphone": "", "ukphone": ""}, {"name": "core books", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freshman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sophomore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "junior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "senior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auditorium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recreation centre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spiral notebook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curriculum vitae", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tertiary education", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preparatory year", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drop out", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curriculum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "draft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enrollment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "register", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enroll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opening ceremony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orientation meeting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "application form", "trans": [], "usphone": "", "ukphone": ""}, {"name": "letter of recommendation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit point", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "score", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assessment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assignment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "presentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "project", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dissertation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "essay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deadline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "journal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "participation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tutorial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "office hour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondary education", "trans": [], "usphone": "", "ukphone": ""}, {"name": "higher education", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adult education", "trans": [], "usphone": "", "ukphone": ""}, {"name": "open admission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kindergarten", "trans": [], "usphone": "", "ukphone": ""}, {"name": "day-care center", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nursery school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elementary school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondary school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "junior high school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "senior high School", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attached middle School", "trans": [], "usphone": "", "ukphone": ""}, {"name": "technical school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polytechnic institute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "student union office", "trans": [], "usphone": "", "ukphone": ""}, {"name": "key school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Graduate School", "trans": [], "usphone": "", "ukphone": ""}, {"name": "open university", "trans": [], "usphone": "", "ukphone": ""}, {"name": "independent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "private school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "public school", "trans": [], "usphone": "", "ukphone": ""}, {"name": "School of Arts and Sciences", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auditor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guest student", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boarder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undergraduate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "postgraduate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Bachelor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Master", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Doctor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alumnus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alumna", "trans": [], "usphone": "", "ukphone": ""}, {"name": "<PERSON>", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graduation appraisal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graduation ceremony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commencement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diploma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graduation certificate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "degree", "trans": [], "usphone": "", "ukphone": ""}, {"name": "associate diploma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "certificate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "qualification", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consultant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "faculty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "teaching assistant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "principal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "president", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coordinator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lecturer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assistant professor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "associate professor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "professor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "school counselor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supervisor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "career advisor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "presentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "project", "trans": [], "usphone": "", "ukphone": ""}, {"name": "report", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adviser", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mentor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chancellor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "president", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cupellation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "term", "trans": [], "usphone": "", "ukphone": ""}, {"name": "semester", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blue-book", "trans": [], "usphone": "", "ukphone": ""}, {"name": "academic year", "trans": [], "usphone": "", "ukphone": ""}, {"name": "final examination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quiz", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oral test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "open-book exam", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pop test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "placement test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aptitude test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "teaching facilities", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sponsor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discipline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "English proficiency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supplementary reading material", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prescribed textbook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "methodology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seminar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lecture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conference hall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "venue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stipend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tuition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scholarship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fellowship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "student union", "trans": [], "usphone": "", "ukphone": ""}, {"name": "schedule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "school timetable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "course arrangement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syllabus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extracurricular", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beginner course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foundation course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basic course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elementary course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primary course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondary course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intermediate course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advanced course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compulsory course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "required course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optional course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elective course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specialized course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "general course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "major", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Archaeology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Architecture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Astronomy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Agriculture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Geography", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Geology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Chemistry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Micro-biology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Marine-biology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Hospitality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Hotel Management", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Botany", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Zoology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Ecology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Electronics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Mathematics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Maths", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Statistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Genetics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Physiology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Literature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Philosophy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "History", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Art", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Sociology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Linguistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Psychology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Engineering", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Business", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Law", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Economics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Finance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Accounting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Banking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Biochemistry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Physics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Anthropology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Science", "trans": [], "usphone": "", "ukphone": ""}, {"name": "touch-screen information service", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "renew ( al )", "trans": [], "usphone": "", "ukphone": ""}, {"name": "due", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overdue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "date of expiry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catalogue under title", "trans": [], "usphone": "", "ukphone": ""}, {"name": "author", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fiction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "non-fiction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reference book section", "trans": [], "usphone": "", "ukphone": ""}, {"name": "encyclopedia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "atlas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "almanac", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magnetically coded", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demagnetize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "available", "trans": [], "usphone": "", "ukphone": ""}, {"name": "in stock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "open shelf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Closed Reserved", "trans": [], "usphone": "", "ukphone": ""}, {"name": "out on loan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reservation list", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prospectus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leaflet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Forbes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "index", "trans": [], "usphone": "", "ukphone": ""}, {"name": "front", "trans": [], "usphone": "", "ukphone": ""}, {"name": "back cover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fly page", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leaf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retrieval", "trans": [], "usphone": "", "ukphone": ""}, {"name": "audio visual resource center", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reprint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "archives", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bound volume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abstract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "borrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "newspaper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "journal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "periodical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "category", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pay fines", "trans": [], "usphone": "", "ukphone": ""}, {"name": "check out", "trans": [], "usphone": "", "ukphone": ""}, {"name": "return in time", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interlibrary service", "trans": [], "usphone": "", "ukphone": ""}, {"name": "information desk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "library card", "trans": [], "usphone": "", "ukphone": ""}, {"name": "call slip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "current issues", "trans": [], "usphone": "", "ukphone": ""}, {"name": "back issues", "trans": [], "usphone": "", "ukphone": ""}, {"name": "librarian and reader", "trans": [], "usphone": "", "ukphone": ""}, {"name": "student card", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loan period", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circulation desk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "internet cafe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lab rules", "trans": [], "usphone": "", "ukphone": ""}, {"name": "printer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fax machine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Macintosh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hardware", "trans": [], "usphone": "", "ukphone": ""}, {"name": "software", "trans": [], "usphone": "", "ukphone": ""}, {"name": "network", "trans": [], "usphone": "", "ukphone": ""}, {"name": "access to the net", "trans": [], "usphone": "", "ukphone": ""}, {"name": "floppy disk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hard disk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "CD-ROM", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keyboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "setup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uninstall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operation system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "program", "trans": [], "usphone": "", "ukphone": ""}, {"name": "word processing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "data processing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "user", "trans": [], "usphone": "", "ukphone": ""}, {"name": "click", "trans": [], "usphone": "", "ukphone": ""}, {"name": "update", "trans": [], "usphone": "", "ukphone": ""}, {"name": "data base", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "menu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "password", "trans": [], "usphone": "", "ukphone": ""}, {"name": "virus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "web page", "trans": [], "usphone": "", "ukphone": ""}, {"name": "website", "trans": [], "usphone": "", "ukphone": ""}, {"name": "online", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Email", "trans": [], "usphone": "", "ukphone": ""}, {"name": "firewall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "log on", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laptop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "have it blank", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feedback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "research", "trans": [], "usphone": "", "ukphone": ""}, {"name": "poll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "survey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "analyze data", "trans": [], "usphone": "", "ukphone": ""}, {"name": "questionnaire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "case study", "trans": [], "usphone": "", "ukphone": ""}, {"name": "observation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hypothesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invalid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "data", "trans": [], "usphone": "", "ukphone": ""}, {"name": "datum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "investigation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quantitative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "qualitative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "significant difference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respondent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interviewee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multiple choice questions", "trans": [], "usphone": "", "ukphone": ""}, {"name": "results", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conclusion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpretation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opinion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fertile soil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hard hoofed animal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cattle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "livestock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaporation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irrigation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solar power", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nuclear power", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alternative energy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carbon dioxide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aluminum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "population density", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pollution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sewage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consumption", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greenhouse effect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "El Nino", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fossil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tribe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Aztec", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ageing population", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meet the criteria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generation of electricity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reservoir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "settlement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pendulum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "innovation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commercialization", "trans": [], "usphone": "", "ukphone": ""}, {"name": "globalization", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utilize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moisture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "season", "trans": [], "usphone": "", "ukphone": ""}, {"name": "timber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "processing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "environment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resource", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recycling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manila folder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treaty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "military", "trans": [], "usphone": "", "ukphone": ""}, {"name": "milestone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "internal clock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depression", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fund raising", "trans": [], "usphone": "", "ukphone": ""}, {"name": "endangered species", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rare species", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kangaroo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "koala", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lizard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ostrich", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penguin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dolphin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "falcon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kiwi", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mammal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reptile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dinosaur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crocodile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "goat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rare lion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elephant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parrot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tiger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marsupial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pouch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "migrant bird", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acrobat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "juggle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ballet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seven-screen cinema", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guitar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comedian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "puppet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trapeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "release", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blockbuster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "folk song", "trans": [], "usphone": "", "ukphone": ""}, {"name": "classic music", "trans": [], "usphone": "", "ukphone": ""}, {"name": "documentary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thriller", "trans": [], "usphone": "", "ukphone": ""}, {"name": "over-weight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sturdy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stocky", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chubby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medium-built", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blonde", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "straight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moustache", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clean-shaven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bald", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wig", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dye", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spectacles", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contact lenses", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "umbrella", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dimple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pimple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freckle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrinkle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheek", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forehead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beauty spot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "short hair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "long hair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high", "trans": [], "usphone": "", "ukphone": ""}, {"name": "short", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "average height", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be in tie", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bow tie", "trans": [], "usphone": "", "ukphone": ""}, {"name": "T-shirt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pullover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sweater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jumper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trousers", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jeans", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sandal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jewelry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laughter line", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thick eyebrows", "trans": [], "usphone": "", "ukphone": ""}, {"name": "long eyelashes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high lined forehead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "small chin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high cheek bones", "trans": [], "usphone": "", "ukphone": ""}, {"name": "full lips", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turn-up nose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "double chin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crew cut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "short spiky hair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formal clothes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "casual clothes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polo shirt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loose clothes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tight clothes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pony tail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plait", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pig tail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wear …hair in a bun", "trans": [], "usphone": "", "ukphone": ""}, {"name": "double deck bus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trolley bus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shuttle bus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "taxi", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cab", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lorry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "truck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trailer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underground", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tube", "trans": [], "usphone": "", "ukphone": ""}, {"name": "metro", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ferry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "row boat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunshade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "windscreen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wheel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steering wheel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tyre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ignition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alarm system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "siren", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mild", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tropical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Portuguese", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Arabic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "German", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Spanish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Mandarin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Cantonese", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dialect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Sino-Tibetan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "make a reservation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "book", "trans": [], "usphone": "", "ukphone": ""}, {"name": "runway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "taxi", "trans": [], "usphone": "", "ukphone": ""}, {"name": "baggage reclaim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "departure lounge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bound for", "trans": [], "usphone": "", "ukphone": ""}, {"name": "take-off", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pilot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flight attendant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stewardess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "international arrival", "trans": [], "usphone": "", "ukphone": ""}, {"name": "departure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "domestic arrival", "trans": [], "usphone": "", "ukphone": ""}, {"name": "departure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "customs", "trans": [], "usphone": "", "ukphone": ""}, {"name": "duty-free shop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "economy class", "trans": [], "usphone": "", "ukphone": ""}, {"name": "first class", "trans": [], "usphone": "", "ukphone": ""}, {"name": "business class", "trans": [], "usphone": "", "ukphone": ""}, {"name": "air crash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drought", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "earthquake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volcano", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tidal wave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tsunami", "trans": [], "usphone": "", "ukphone": ""}, {"name": "air crash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drought", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tsunami", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tornado", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twister", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hurricane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "typhoon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disaster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plague", "trans": [], "usphone": "", "ukphone": ""}, {"name": "casualty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "death toll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fatality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "news bulletin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "financial assistance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rescue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wreckage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire brigade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medical team", "trans": [], "usphone": "", "ukphone": ""}, {"name": "derail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "survivor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toll-free numb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hotline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ambulance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire engine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "police car", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vacant position", "trans": [], "usphone": "", "ukphone": ""}, {"name": "applicant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "job interview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "personnel manager", "trans": [], "usphone": "", "ukphone": ""}, {"name": "full-time job", "trans": [], "usphone": "", "ukphone": ""}, {"name": "part-time job", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cashier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "day shift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shift work", "trans": [], "usphone": "", "ukphone": ""}, {"name": "window dressing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "routine work", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "challenging", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stimulating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interesting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "full of adventure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "on the left", "trans": [], "usphone": "", "ukphone": ""}, {"name": "on the right", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turn to the left", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turn to the right", "trans": [], "usphone": "", "ukphone": ""}, {"name": "around the corner to the left", "trans": [], "usphone": "", "ukphone": ""}, {"name": "around the corner to theright", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be in front of", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be behind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be beside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be on the corner of", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be parallel to", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cross", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corridor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "path", "trans": [], "usphone": "", "ukphone": ""}, {"name": "passage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "signpost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elevator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "escalator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pedestrian crossing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zebra crossing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pelican crossing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pavement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sidewalk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overpass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underpass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "road", "trans": [], "usphone": "", "ukphone": ""}, {"name": "street", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boulevard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be far from", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be nearby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go straight across", "trans": [], "usphone": "", "ukphone": ""}, {"name": "through", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cross (over)", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be next to", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go up", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go down", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go back", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go on", "trans": [], "usphone": "", "ukphone": ""}, {"name": "along...till...", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be the first from the left", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opposite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "across from", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be located behind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "be in the corner of", "trans": [], "usphone": "", "ukphone": ""}, {"name": "step", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wheelchair access", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intersection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crossroad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "a fork on the road", "trans": [], "usphone": "", "ukphone": ""}, {"name": "a T road", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ground floor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "annex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twin building", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landmark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Belfast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Birmingham", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Cardiff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Coventry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Edinburgh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "England", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Leeds", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Liverpool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "London", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Manchester", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Northern Ireland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Scotland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Sheffield", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Wales", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Dublin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Adelaide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Brisbane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Canberra", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Cairns", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Melbourne", "trans": [], "usphone": "", "ukphone": ""}, {"name": "New South Wales", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Perth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Queensland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Sydney", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Victoria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Auckland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Christchurch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Wellington", "trans": [], "usphone": "", "ukphone": ""}, {"name": "British Columbia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Montreal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Ontario", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Ottawa", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Quebec", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Toronto", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Vancouver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Victoria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Atlanta", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Boston", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Chicago", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Detroit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Houston", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Los Angeles", "trans": [], "usphone": "", "ukphone": ""}, {"name": "New York", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Philadelphia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "San Franciso", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Seattle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Washington D. C.", "trans": [], "usphone": "", "ukphone": ""}]