[{"name": "file", "trans": [], "usphone": "", "ukphone": ""}, {"name": "command", "trans": [], "usphone": "", "ukphone": ""}, {"name": "use", "trans": [], "usphone": "", "ukphone": ""}, {"name": "program", "trans": [], "usphone": "", "ukphone": ""}, {"name": "line", "trans": [], "usphone": "", "ukphone": ""}, {"name": "if", "trans": [], "usphone": "", "ukphone": ""}, {"name": "display", "trans": [], "usphone": "", "ukphone": ""}, {"name": "set", "trans": [], "usphone": "", "ukphone": ""}, {"name": "key", "trans": [], "usphone": "", "ukphone": ""}, {"name": "list", "trans": [], "usphone": "", "ukphone": ""}, {"name": "by", "trans": [], "usphone": "", "ukphone": ""}, {"name": "press", "trans": [], "usphone": "", "ukphone": ""}, {"name": "with", "trans": [], "usphone": "", "ukphone": ""}, {"name": "format", "trans": [], "usphone": "", "ukphone": ""}, {"name": "change", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cursor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "directory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "from", "trans": [], "usphone": "", "ukphone": ""}, {"name": "menu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "option", "trans": [], "usphone": "", "ukphone": ""}, {"name": "character", "trans": [], "usphone": "", "ukphone": ""}, {"name": "current", "trans": [], "usphone": "", "ukphone": ""}, {"name": "type", "trans": [], "usphone": "", "ukphone": ""}, {"name": "screen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "move", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "text", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "see", "trans": [], "usphone": "", "ukphone": ""}, {"name": "name", "trans": [], "usphone": "", "ukphone": ""}, {"name": "record", "trans": [], "usphone": "", "ukphone": ""}, {"name": "box", "trans": [], "usphone": "", "ukphone": ""}, {"name": "database", "trans": [], "usphone": "", "ukphone": ""}, {"name": "help", "trans": [], "usphone": "", "ukphone": ""}, {"name": "memory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "which", "trans": [], "usphone": "", "ukphone": ""}, {"name": "all", "trans": [], "usphone": "", "ukphone": ""}, {"name": "on", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "margin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "also", "trans": [], "usphone": "", "ukphone": ""}, {"name": "do", "trans": [], "usphone": "", "ukphone": ""}, {"name": "information", "trans": [], "usphone": "", "ukphone": ""}, {"name": "choose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "select", "trans": [], "usphone": "", "ukphone": ""}, {"name": "group", "trans": [], "usphone": "", "ukphone": ""}, {"name": "first", "trans": [], "usphone": "", "ukphone": ""}, {"name": "field", "trans": [], "usphone": "", "ukphone": ""}, {"name": "procedure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "print", "trans": [], "usphone": "", "ukphone": ""}, {"name": "return", "trans": [], "usphone": "", "ukphone": ""}, {"name": "number", "trans": [], "usphone": "", "ukphone": ""}, {"name": "selected", "trans": [], "usphone": "", "ukphone": ""}, {"name": "want", "trans": [], "usphone": "", "ukphone": ""}, {"name": "window", "trans": [], "usphone": "", "ukphone": ""}, {"name": "message", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dialog", "trans": [], "usphone": "", "ukphone": ""}, {"name": "example", "trans": [], "usphone": "", "ukphone": ""}, {"name": "create", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "related", "trans": [], "usphone": "", "ukphone": ""}, {"name": "item", "trans": [], "usphone": "", "ukphone": ""}, {"name": "edit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marked", "trans": [], "usphone": "", "ukphone": ""}, {"name": "area", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parameter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "then", "trans": [], "usphone": "", "ukphone": ""}, {"name": "variable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tab", "trans": [], "usphone": "", "ukphone": ""}, {"name": "up", "trans": [], "usphone": "", "ukphone": ""}, {"name": "string", "trans": [], "usphone": "", "ukphone": ""}, {"name": "each", "trans": [], "usphone": "", "ukphone": ""}, {"name": "active", "trans": [], "usphone": "", "ukphone": ""}, {"name": "topic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "start", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mode", "trans": [], "usphone": "", "ukphone": ""}, {"name": "selection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "function", "trans": [], "usphone": "", "ukphone": ""}, {"name": "word", "trans": [], "usphone": "", "ukphone": ""}, {"name": "make", "trans": [], "usphone": "", "ukphone": ""}, {"name": "right", "trans": [], "usphone": "", "ukphone": ""}, {"name": "value", "trans": [], "usphone": "", "ukphone": ""}, {"name": "button", "trans": [], "usphone": "", "ukphone": ""}, {"name": "index", "trans": [], "usphone": "", "ukphone": ""}, {"name": "without", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "left", "trans": [], "usphone": "", "ukphone": ""}, {"name": "save", "trans": [], "usphone": "", "ukphone": ""}, {"name": "next", "trans": [], "usphone": "", "ukphone": ""}, {"name": "off", "trans": [], "usphone": "", "ukphone": ""}, {"name": "following", "trans": [], "usphone": "", "ukphone": ""}, {"name": "control", "trans": [], "usphone": "", "ukphone": ""}, {"name": "only", "trans": [], "usphone": "", "ukphone": ""}, {"name": "user", "trans": [], "usphone": "", "ukphone": ""}, {"name": "end", "trans": [], "usphone": "", "ukphone": ""}, {"name": "system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "letter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "data", "trans": [], "usphone": "", "ukphone": ""}, {"name": "setting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "position", "trans": [], "usphone": "", "ukphone": ""}, {"name": "down", "trans": [], "usphone": "", "ukphone": ""}, {"name": "task", "trans": [], "usphone": "", "ukphone": ""}, {"name": "view", "trans": [], "usphone": "", "ukphone": ""}, {"name": "switch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "get", "trans": [], "usphone": "", "ukphone": ""}, {"name": "default", "trans": [], "usphone": "", "ukphone": ""}, {"name": "structure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "into", "trans": [], "usphone": "", "ukphone": ""}, {"name": "path", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blank", "trans": [], "usphone": "", "ukphone": ""}, {"name": "open", "trans": [], "usphone": "", "ukphone": ""}, {"name": "add", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "erase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "filename", "trans": [], "usphone": "", "ukphone": ""}, {"name": "search", "trans": [], "usphone": "", "ukphone": ""}, {"name": "another", "trans": [], "usphone": "", "ukphone": ""}, {"name": "last", "trans": [], "usphone": "", "ukphone": ""}, {"name": "column", "trans": [], "usphone": "", "ukphone": ""}, {"name": "after", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prompt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "two", "trans": [], "usphone": "", "ukphone": ""}, {"name": "execute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "about", "trans": [], "usphone": "", "ukphone": ""}, {"name": "escape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "error", "trans": [], "usphone": "", "ukphone": ""}, {"name": "currently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "same", "trans": [], "usphone": "", "ukphone": ""}, {"name": "status", "trans": [], "usphone": "", "ukphone": ""}, {"name": "run", "trans": [], "usphone": "", "ukphone": ""}, {"name": "argument", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "store", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scroll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "macro", "trans": [], "usphone": "", "ukphone": ""}, {"name": "page", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "define", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "other", "trans": [], "usphone": "", "ukphone": ""}, {"name": "while", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pressing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "top", "trans": [], "usphone": "", "ukphone": ""}, {"name": "how", "trans": [], "usphone": "", "ukphone": ""}, {"name": "color", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "block", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decimal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "main", "trans": [], "usphone": "", "ukphone": ""}, {"name": "definition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "between", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "date", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remove", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "label", "trans": [], "usphone": "", "ukphone": ""}, {"name": "within", "trans": [], "usphone": "", "ukphone": ""}, {"name": "issue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "different", "trans": [], "usphone": "", "ukphone": ""}, {"name": "available", "trans": [], "usphone": "", "ukphone": ""}, {"name": "returned", "trans": [], "usphone": "", "ukphone": ""}, {"name": "associate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "before", "trans": [], "usphone": "", "ukphone": ""}, {"name": "order", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "array", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "note", "trans": [], "usphone": "", "ukphone": ""}, {"name": "locate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "video", "trans": [], "usphone": "", "ukphone": ""}, {"name": "printer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bottom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carriage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "content", "trans": [], "usphone": "", "ukphone": ""}, {"name": "either", "trans": [], "usphone": "", "ukphone": ""}, {"name": "space", "trans": [], "usphone": "", "ukphone": ""}, {"name": "editor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paragraph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "report", "trans": [], "usphone": "", "ukphone": ""}, {"name": "execution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "version", "trans": [], "usphone": "", "ukphone": ""}, {"name": "find", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pointer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keyboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "full", "trans": [], "usphone": "", "ukphone": ""}, {"name": "check", "trans": [], "usphone": "", "ukphone": ""}, {"name": "should", "trans": [], "usphone": "", "ukphone": ""}, {"name": "single", "trans": [], "usphone": "", "ukphone": ""}, {"name": "positioning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "provide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "title", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expression", "trans": [], "usphone": "", "ukphone": ""}, {"name": "through", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toggle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "code", "trans": [], "usphone": "", "ukphone": ""}, {"name": "such", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beginning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tree", "trans": [], "usphone": "", "ukphone": ""}, {"name": "environment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "but", "trans": [], "usphone": "", "ukphone": ""}, {"name": "device", "trans": [], "usphone": "", "ukphone": ""}, {"name": "highlight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "call", "trans": [], "usphone": "", "ukphone": ""}, {"name": "continue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "until", "trans": [], "usphone": "", "ukphone": ""}, {"name": "begin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "place", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rename", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "work", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "close", "trans": [], "usphone": "", "ukphone": ""}, {"name": "combination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "so", "trans": [], "usphone": "", "ukphone": ""}, {"name": "except", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "back", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "section", "trans": [], "usphone": "", "ukphone": ""}, {"name": "follow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "split", "trans": [], "usphone": "", "ukphone": ""}, {"name": "need", "trans": [], "usphone": "", "ukphone": ""}, {"name": "access", "trans": [], "usphone": "", "ukphone": ""}, {"name": "additional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cancel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "document", "trans": [], "usphone": "", "ukphone": ""}, {"name": "case", "trans": [], "usphone": "", "ukphone": ""}, {"name": "numeric", "trans": [], "usphone": "", "ukphone": ""}, {"name": "go", "trans": [], "usphone": "", "ukphone": ""}, {"name": "load", "trans": [], "usphone": "", "ukphone": ""}, {"name": "try", "trans": [], "usphone": "", "ukphone": ""}, {"name": "size", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "history", "trans": [], "usphone": "", "ukphone": ""}, {"name": "second", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reflow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "output", "trans": [], "usphone": "", "ukphone": ""}, {"name": "out", "trans": [], "usphone": "", "ukphone": ""}, {"name": "both", "trans": [], "usphone": "", "ukphone": ""}, {"name": "install", "trans": [], "usphone": "", "ukphone": ""}, {"name": "source", "trans": [], "usphone": "", "ukphone": ""}, {"name": "way", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "support", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specific", "trans": [], "usphone": "", "ukphone": ""}, {"name": "join", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "like", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diskette", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "application", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confirmation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whether", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "click", "trans": [], "usphone": "", "ukphone": ""}, {"name": "write", "trans": [], "usphone": "", "ukphone": ""}, {"name": "byte", "trans": [], "usphone": "", "ukphone": ""}, {"name": "show", "trans": [], "usphone": "", "ukphone": ""}, {"name": "otherwise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "working", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delimiter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "location", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graphic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "read", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confirm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "once", "trans": [], "usphone": "", "ukphone": ""}, {"name": "however", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "look", "trans": [], "usphone": "", "ukphone": ""}, {"name": "starting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "now", "trans": [], "usphone": "", "ukphone": ""}, {"name": "original", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correspond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "property", "trans": [], "usphone": "", "ukphone": ""}, {"name": "several", "trans": [], "usphone": "", "ukphone": ""}, {"name": "learn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bracket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "omit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "running", "trans": [], "usphone": "", "ukphone": ""}, {"name": "edge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "form", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instruction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "below", "trans": [], "usphone": "", "ukphone": ""}, {"name": "standard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occurrence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "append", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "password", "trans": [], "usphone": "", "ukphone": ""}, {"name": "point", "trans": [], "usphone": "", "ukphone": ""}, {"name": "variety", "trans": [], "usphone": "", "ukphone": ""}, {"name": "many", "trans": [], "usphone": "", "ukphone": ""}, {"name": "buffer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "useful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "object", "trans": [], "usphone": "", "ukphone": ""}, {"name": "again", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "update", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coprocessor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overlay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "practice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "navigation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "automatically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "total", "trans": [], "usphone": "", "ukphone": ""}, {"name": "previous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "software", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shortcut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "long", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unique", "trans": [], "usphone": "", "ukphone": ""}, {"name": "part", "trans": [], "usphone": "", "ukphone": ""}, {"name": "updated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "internal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "math", "trans": [], "usphone": "", "ukphone": ""}, {"name": "since", "trans": [], "usphone": "", "ukphone": ""}, {"name": "determine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "making", "trans": [], "usphone": "", "ukphone": ""}, {"name": "center", "trans": [], "usphone": "", "ukphone": ""}, {"name": "already", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keyword", "trans": [], "usphone": "", "ukphone": ""}, {"name": "action", "trans": [], "usphone": "", "ukphone": ""}, {"name": "condition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assigned", "trans": [], "usphone": "", "ukphone": ""}, {"name": "give", "trans": [], "usphone": "", "ukphone": ""}, {"name": "large", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chapter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "computer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "past", "trans": [], "usphone": "", "ukphone": ""}, {"name": "match", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "always", "trans": [], "usphone": "", "ukphone": ""}, {"name": "require", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opening", "trans": [], "usphone": "", "ukphone": ""}, {"name": "network", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "release", "trans": [], "usphone": "", "ukphone": ""}, {"name": "three", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deletion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fixed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amount", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alias", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "else", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maximum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "under", "trans": [], "usphone": "", "ukphone": ""}, {"name": "take", "trans": [], "usphone": "", "ukphone": ""}, {"name": "switching", "trans": [], "usphone": "", "ukphone": ""}, {"name": "element", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modification", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modified", "trans": [], "usphone": "", "ukphone": ""}, {"name": "input", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uppercase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "found", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "force", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lowercase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "just", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "environ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "why", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temporary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "put", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "encounter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "across", "trans": [], "usphone": "", "ukphone": ""}, {"name": "matching", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wildcard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "level", "trans": [], "usphone": "", "ukphone": ""}, {"name": "browse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "speech", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "memo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loaded", "trans": [], "usphone": "", "ukphone": ""}, {"name": "length", "trans": [], "usphone": "", "ukphone": ""}, {"name": "round", "trans": [], "usphone": "", "ukphone": ""}, {"name": "variant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "floppy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "machine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "square", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "home", "trans": [], "usphone": "", "ukphone": ""}, {"name": "normal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "onto", "trans": [], "usphone": "", "ukphone": ""}, {"name": "during", "trans": [], "usphone": "", "ukphone": ""}, {"name": "module", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monochrome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assistance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "library", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "even", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaluate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "times", "trans": [], "usphone": "", "ukphone": ""}, {"name": "previously", "trans": [], "usphone": "", "ukphone": ""}, {"name": "directly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "template", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "later", "trans": [], "usphone": "", "ukphone": ""}, {"name": "driver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "therefore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "linker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "process", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scheme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "every", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "possible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "above", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "result", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syntax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hidden", "trans": [], "usphone": "", "ukphone": ""}, {"name": "null", "trans": [], "usphone": "", "ukphone": ""}, {"name": "send", "trans": [], "usphone": "", "ukphone": ""}, {"name": "private", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hardware", "trans": [], "usphone": "", "ukphone": ""}, {"name": "say", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alternate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collapse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "present", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpreter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "special", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regardless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compatible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "empty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alphabetical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "branch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multiple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "configuration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replacement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "required", "trans": [], "usphone": "", "ukphone": ""}, {"name": "macros", "trans": [], "usphone": "", "ukphone": ""}, {"name": "table", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "batch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aboveboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "activate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "around", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "floating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refresh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "public", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eject", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ignore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "share", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sequence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "step", "trans": [], "usphone": "", "ukphone": ""}, {"name": "double", "trans": [], "usphone": "", "ukphone": ""}, {"name": "come", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "describe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "count", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suspend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enhance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "separate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "echo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "necessary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "able", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ask", "trans": [], "usphone": "", "ukphone": ""}, {"name": "term", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "warning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "less", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expanding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "on-", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reorder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "direct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enclose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "various", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prevent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "side", "trans": [], "usphone": "", "ukphone": ""}, {"name": "push", "trans": [], "usphone": "", "ukphone": ""}, {"name": "programming", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "row", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pressed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temporarily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "day", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repaint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "redefine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dimension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boundary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zoom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "initialize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "personal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hello", "trans": [], "usphone": "", "ukphone": ""}, {"name": "true", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "font", "trans": [], "usphone": "", "ukphone": ""}, {"name": "know", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "global", "trans": [], "usphone": "", "ukphone": ""}, {"name": "still", "trans": [], "usphone": "", "ukphone": ""}, {"name": "installation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invoke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interactive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "described", "trans": [], "usphone": "", "ukphone": ""}, {"name": "century", "trans": [], "usphone": "", "ukphone": ""}, {"name": "literal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exclusive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wait", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appropriate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adapter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "filter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "break", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "searching", "trans": [], "usphone": "", "ukphone": ""}, {"name": "receive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "normally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exactly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "immediately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "separated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equivalent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "light", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zero", "trans": [], "usphone": "", "ukphone": ""}, {"name": "storage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "width", "trans": [], "usphone": "", "ukphone": ""}, {"name": "language", "trans": [], "usphone": "", "ukphone": ""}, {"name": "startup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "much", "trans": [], "usphone": "", "ukphone": ""}, {"name": "per", "trans": [], "usphone": "", "ukphone": ""}, {"name": "over", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mirror", "trans": [], "usphone": "", "ukphone": ""}, {"name": "request", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keypad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resident", "trans": [], "usphone": "", "ukphone": ""}, {"name": "learning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "talk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "summary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "well", "trans": [], "usphone": "", "ukphone": ""}, {"name": "link", "trans": [], "usphone": "", "ukphone": ""}, {"name": "according", "trans": [], "usphone": "", "ukphone": ""}, {"name": "identify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "designated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pertain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expansion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incompatible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blinking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "month", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precede", "trans": [], "usphone": "", "ukphone": ""}, {"name": "readily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transportable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appropriately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "routine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ready", "trans": [], "usphone": "", "ukphone": ""}, {"name": "listing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "newly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "year", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "session", "trans": [], "usphone": "", "ukphone": ""}, {"name": "own", "trans": [], "usphone": "", "ukphone": ""}, {"name": "redraw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "here", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "particular", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rectangle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "additive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "similar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assembly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copyright", "trans": [], "usphone": "", "ukphone": ""}, {"name": "description", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retrieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mistake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "produce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exception", "trans": [], "usphone": "", "ukphone": ""}, {"name": "digit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minimum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "although", "trans": [], "usphone": "", "ukphone": ""}, {"name": "re", "trans": [], "usphone": "", "ukphone": ""}, {"name": "third", "trans": [], "usphone": "", "ukphone": ""}, {"name": "red", "trans": [], "usphone": "", "ukphone": ""}, {"name": "along", "trans": [], "usphone": "", "ukphone": ""}, {"name": "test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "small", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "company", "trans": [], "usphone": "", "ukphone": ""}, {"name": "movie", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frequently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undefined", "trans": [], "usphone": "", "ukphone": ""}, {"name": "state", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accept", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "documentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "asterisk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "easily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "become", "trans": [], "usphone": "", "ukphone": ""}, {"name": "address", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interface", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repeat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assumed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "speed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "combine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finished", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mixed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formatting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "root", "trans": [], "usphone": "", "ukphone": ""}, {"name": "symbol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "binary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whenever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "caution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subtotal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "card", "trans": [], "usphone": "", "ukphone": ""}, {"name": "general", "trans": [], "usphone": "", "ukphone": ""}, {"name": "associated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transfer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "connect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "partition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hexadecimal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specification", "trans": [], "usphone": "", "ukphone": ""}, {"name": "customize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "far", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "duplicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compression", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "means", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alternately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intensity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reading", "trans": [], "usphone": "", "ukphone": ""}, {"name": "let", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explicitly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sector", "trans": [], "usphone": "", "ukphone": ""}, {"name": "problem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vertically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horizontally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backspace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "people", "trans": [], "usphone": "", "ukphone": ""}, {"name": "short", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formatted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underscore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correctly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "initially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reformat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "integrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "controlled", "trans": [], "usphone": "", "ukphone": ""}, {"name": "period", "trans": [], "usphone": "", "ukphone": ""}, {"name": "huge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "determined", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trailing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seek", "trans": [], "usphone": "", "ukphone": ""}, {"name": "introduction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "base", "trans": [], "usphone": "", "ukphone": ""}, {"name": "integer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attempt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subscript", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tiny", "trans": [], "usphone": "", "ukphone": ""}, {"name": "model", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opened", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sun", "trans": [], "usphone": "", "ukphone": ""}, {"name": "translate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reason", "trans": [], "usphone": "", "ukphone": ""}, {"name": "colon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "avoid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "range", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allocate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "simply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "verify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "direction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "portion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "successful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "applied", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "achieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "together", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "free", "trans": [], "usphone": "", "ukphone": ""}, {"name": "properly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "splitting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "console", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kernel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "easy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modifier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invalid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compiler", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "face", "trans": [], "usphone": "", "ukphone": ""}, {"name": "random", "trans": [], "usphone": "", "ukphone": ""}, {"name": "facility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heading", "trans": [], "usphone": "", "ukphone": ""}, {"name": "asynchronous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "series", "trans": [], "usphone": "", "ukphone": ""}, {"name": "individual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paste", "trans": [], "usphone": "", "ukphone": ""}, {"name": "welcome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "six", "trans": [], "usphone": "", "ukphone": ""}, {"name": "early", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "queue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interrupt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "converted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "common", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hyphen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "serial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loading", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "setup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explanation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "certain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "archive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "image", "trans": [], "usphone": "", "ukphone": ""}, {"name": "platform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "often", "trans": [], "usphone": "", "ukphone": ""}, {"name": "signal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cpu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fully", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deactivate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "especially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "usually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recommend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maintain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "important", "trans": [], "usphone": "", "ukphone": ""}, {"name": "central", "trans": [], "usphone": "", "ukphone": ""}, {"name": "addition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anytime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "analyst", "trans": [], "usphone": "", "ukphone": ""}, {"name": "false", "trans": [], "usphone": "", "ukphone": ""}, {"name": "black", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cycle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "offer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ending", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sentence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remember", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "design", "trans": [], "usphone": "", "ukphone": ""}, {"name": "examine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "initial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corrupt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "buy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "increase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "host", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sample", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pending", "trans": [], "usphone": "", "ukphone": ""}, {"name": "divide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "half", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magenta", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leading", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrong", "trans": [], "usphone": "", "ukphone": ""}, {"name": "today", "trans": [], "usphone": "", "ukphone": ""}, {"name": "least", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opposite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "white", "trans": [], "usphone": "", "ukphone": ""}, {"name": "override", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "damage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reserved", "trans": [], "usphone": "", "ukphone": ""}, {"name": "technology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "payment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kilobyte", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parenthesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "locating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "developer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "murder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flush", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unlock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "movement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consecutive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "front", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prefix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carousel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safety", "trans": [], "usphone": "", "ukphone": ""}, {"name": "static", "trans": [], "usphone": "", "ukphone": ""}, {"name": "background", "trans": [], "usphone": "", "ukphone": ""}, {"name": "product", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assignment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "declare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adjust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recognize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "route", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respectively", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unsuccessful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "received", "trans": [], "usphone": "", "ukphone": ""}, {"name": "navigate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "considered", "trans": [], "usphone": "", "ukphone": ""}, {"name": "due", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "descend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "track", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precedence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skeleton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "log", "trans": [], "usphone": "", "ukphone": ""}, {"name": "star", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replaceable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accessible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "involve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "configure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "question", "trans": [], "usphone": "", "ukphone": ""}, {"name": "green", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entirely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "helpful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "middle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "declared", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graphically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auto", "trans": [], "usphone": "", "ukphone": ""}, {"name": "automatic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aligned", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anywhere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terminal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "door", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resolution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "local", "trans": [], "usphone": "", "ukphone": ""}, {"name": "semicolon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reread", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overwrite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "critical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manager", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capability", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affected", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allowed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "border", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cache", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "play", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quickly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fastback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "answer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "represent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "difference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "highest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "project", "trans": [], "usphone": "", "ukphone": ""}, {"name": "physical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "matter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "publisher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substitute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disabled", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "positive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upgrade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "happen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elapsed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "future", "trans": [], "usphone": "", "ukphone": ""}, {"name": "midnight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "though", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mono", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "throughout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "via", "trans": [], "usphone": "", "ukphone": ""}, {"name": "among", "trans": [], "usphone": "", "ukphone": ""}, {"name": "neither", "trans": [], "usphone": "", "ukphone": ""}, {"name": "layer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scatter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conventional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handler", "trans": [], "usphone": "", "ukphone": ""}, {"name": "processor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desktop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "build", "trans": [], "usphone": "", "ukphone": ""}, {"name": "windowing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "development", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exceed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "understand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horizontal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alphabetically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reserve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manifest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disconnect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clockwise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eliminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "actual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "declaration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "probably", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indicator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "icon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consideration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "picture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "layout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suggest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convenient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instruct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appendix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "truncate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inhibit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nearly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "warn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "register", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stuff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exclude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destroy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calculation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "angle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lexical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trouble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "customer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "port", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discuss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "segment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "filing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "identically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "market", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valuable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limited", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trying", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permanently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accelerator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "originally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ability", "trans": [], "usphone": "", "ukphone": ""}, {"name": "internally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "derelict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "header", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extra", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repeated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "death", "trans": [], "usphone": "", "ukphone": ""}, {"name": "observe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "density", "trans": [], "usphone": "", "ukphone": ""}, {"name": "management", "trans": [], "usphone": "", "ukphone": ""}, {"name": "environmental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surrounding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "master", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recursive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dimensional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conjunction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "identical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meaning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interval", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compatibility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "criterion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "office", "trans": [], "usphone": "", "ukphone": ""}, {"name": "express", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "activity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "odometer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phoenix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obtain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "easel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "latter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decrease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mainframe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diacritical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confidential", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "division", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regular", "trans": [], "usphone": "", "ukphone": ""}, {"name": "implicit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "near", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fifth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whereas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "review", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whatever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "align", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yellow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "micro", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beyond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "against", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "service", "trans": [], "usphone": "", "ukphone": ""}, {"name": "little", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhaust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "choice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sounding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "develop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "holding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alpha", "trans": [], "usphone": "", "ukphone": ""}, {"name": "constant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "warranty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trigger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lesson", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "busy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "usage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "difficult", "trans": [], "usphone": "", "ukphone": ""}, {"name": "failure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "communication", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exclamation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whole", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "connection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "connectivity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "translation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dynamic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foreground", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preserve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "necessarily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "differ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stationary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thereafter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limiting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restructure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delimit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "separately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "classify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interfere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "individually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vertical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undesirable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "piece", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unavailable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unlike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insufficient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "map", "trans": [], "usphone": "", "ukphone": ""}, {"name": "figure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prepare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consider", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convenience", "trans": [], "usphone": "", "ukphone": ""}, {"name": "method", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pacific", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strong", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emphasize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "department", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forced", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permanent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "away", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concatenate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lightning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "additionally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accidentally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concept", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optimize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "counter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subsequently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "registration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "city", "trans": [], "usphone": "", "ukphone": ""}, {"name": "designate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consult", "trans": [], "usphone": "", "ukphone": ""}, {"name": "completely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "virtually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substantially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specialize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primarily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sequentially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "client", "trans": [], "usphone": "", "ukphone": ""}, {"name": "runtime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "author", "trans": [], "usphone": "", "ukphone": ""}, {"name": "programmer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commercial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "particularly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "low", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sheet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "employee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "legal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "qualified", "trans": [], "usphone": "", "ukphone": ""}, {"name": "context", "trans": [], "usphone": "", "ukphone": ""}, {"name": "involved", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conditional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "halfway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oriented", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "week", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subroutine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bracketed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "autoindex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restrict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "performance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "showing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distribution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "denote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repeatedly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mega", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rebuild", "trans": [], "usphone": "", "ukphone": ""}, {"name": "certainty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "controller", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pseudo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "administrator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ensemble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allowable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limitations", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restriction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "height", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remainder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organization", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resulting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "external", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adequate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpretability", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indexing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "board", "trans": [], "usphone": "", "ukphone": ""}, {"name": "package", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insertion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intervene", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conflict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "really", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overflow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "virtual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snapshot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sensitivity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "familiar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incorrect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lowest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "simple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subsequent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capitalized", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "noted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desirable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substitution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forget", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keyed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overstrike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tornado", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quotation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ones", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experience", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manufacture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hundred", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thousand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twentieth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "understanding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restricting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fancy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "somewhat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quiet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mod", "trans": [], "usphone": "", "ukphone": ""}, {"name": "numeral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whichever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purchase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "care", "trans": [], "usphone": "", "ukphone": ""}, {"name": "watch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "endeavor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mismatch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "printout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ellipsis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "british", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parallel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "custom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "congratulation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pattern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stopping", "trans": [], "usphone": "", "ukphone": ""}, {"name": "factory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "implement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ampersand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "power", "trans": [], "usphone": "", "ukphone": ""}, {"name": "difficulty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proprietary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aware", "trans": [], "usphone": "", "ukphone": ""}, {"name": "numerous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vowel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "closely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accuracy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traditional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "synchronization", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fragment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "habit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comprise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landler", "trans": [], "usphone": "", "ukphone": ""}, {"name": "absence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revolutionize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "constantly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seldom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unfortunately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expunge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "security", "trans": [], "usphone": "", "ukphone": ""}, {"name": "touch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contrast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reflect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unshift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complexity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "creation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unknown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greatly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "degrade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suggestion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "real", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experimentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experiment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substantial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "announce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "squeeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "father", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reinstate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tutorial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nicety", "trans": [], "usphone": "", "ukphone": ""}, {"name": "roll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exponent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exponential", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prefer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complicated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reactivate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spread", "trans": [], "usphone": "", "ukphone": ""}, {"name": "synchronize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "widely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "very", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unnecessary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unchanged", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cross", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slowly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inexperienced", "trans": [], "usphone": "", "ukphone": ""}, {"name": "noninteractive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unwanted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unused", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unmarked", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nothing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dearly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extremely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hardly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "placement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "think", "trans": [], "usphone": "", "ukphone": ""}, {"name": "technical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "idea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stamp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indirectly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smooth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attached", "trans": [], "usphone": "", "ukphone": ""}, {"name": "average", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quietly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "never", "trans": [], "usphone": "", "ukphone": ""}, {"name": "initiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "powerful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purpose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "daily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "possibly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "potentially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moreover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "american", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "world", "trans": [], "usphone": "", "ukphone": ""}, {"name": "independent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "independently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "continuously", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shield", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "happening", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transaction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emulation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occasionally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "probable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "talent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "financial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logged", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ware", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disregard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "waiting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preceding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comparison", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advanced", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "programmable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "readable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recoverable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "possibility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "applicable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "printable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "executable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "essentially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "familiarize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "employe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suitable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpretable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fourscore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "teach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "procedural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phrase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specifically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penalty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "violate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indefinitely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "major", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "becoming", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enjoy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disappear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diagonally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "labeled", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "significant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "avail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hang", "trans": [], "usphone": "", "ukphone": ""}, {"name": "craze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consequently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "introduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "team", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acknowledgment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "efficiently", "trans": [], "usphone": "", "ukphone": ""}, {"name": "predict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anticipate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bypass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "natural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logarithm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reappears", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reload", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occupy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "photograph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terminating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resolve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unsafe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "separator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hierarchical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assortment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "growing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discussion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alphabet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scattered", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eventually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subgroup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "superimpose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reorganization", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rewrite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "university", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "closed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repeating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repetitive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reenter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rearrange", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rectangular", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suppose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supposed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "masking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "price", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demonstrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "importance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pipe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turnkey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restricted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suspension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seamless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clipper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unsigned", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unformatted", "trans": [], "usphone": "", "ukphone": ""}, {"name": "useless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limiter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mountain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "redundant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dependent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contiguous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consistent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multiprocessing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "architecture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "structural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outcome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "association", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opinion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpret", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explanatory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assemble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assembler", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arithmetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "varying", "trans": [], "usphone": "", "ukphone": ""}, {"name": "representative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "typical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sufficient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "caret", "trans": [], "usphone": "", "ukphone": ""}, {"name": "socket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protocol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "presence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "telephone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "social", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equipment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lending", "trans": [], "usphone": "", "ukphone": ""}, {"name": "book", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circumstances", "trans": [], "usphone": "", "ukphone": ""}, {"name": "situation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "please", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mixture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "representation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "esoteric", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "final", "trans": [], "usphone": "", "ukphone": ""}, {"name": "physically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "successive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "succession", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unpack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alignment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "typewriter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "big", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sensitive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reduction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terminology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ascending", "trans": [], "usphone": "", "ukphone": ""}, {"name": "augment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "increment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stream", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obsolete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accommodate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motif", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subject", "trans": [], "usphone": "", "ukphone": ""}, {"name": "job", "trans": [], "usphone": "", "ukphone": ""}, {"name": "differentiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distinction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distinguish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "locking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "progress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fundamental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underlying", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vital", "trans": [], "usphone": "", "ukphone": ""}, {"name": "national", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agree", "trans": [], "usphone": "", "ukphone": ""}, {"name": "iterative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inclusive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exceeded", "trans": [], "usphone": "", "ukphone": ""}, {"name": "numerical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "digital", "trans": [], "usphone": "", "ukphone": ""}, {"name": "combo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cord", "trans": [], "usphone": "", "ukphone": ""}]