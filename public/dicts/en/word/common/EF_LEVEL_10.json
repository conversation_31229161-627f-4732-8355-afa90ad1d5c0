[{"name": "merger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "morale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pressure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rumor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smoothly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "task", "trans": [], "usphone": "", "ukphone": ""}, {"name": "CEO", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discuss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purpose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "item", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negotiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regret", "trans": [], "usphone": "", "ukphone": ""}, {"name": "congratulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accurate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agenda", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aware", "trans": [], "usphone": "", "ukphone": ""}, {"name": "budget", "trans": [], "usphone": "", "ukphone": ""}, {"name": "budget", "trans": [], "usphone": "", "ukphone": ""}, {"name": "challenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conclude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaluate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "executive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "further", "trans": [], "usphone": "", "ukphone": ""}, {"name": "integrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "involve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "old-fashioned", "trans": [], "usphone": "", "ukphone": ""}, {"name": "courteous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generational", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sympathize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "implement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "structure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "process", "trans": [], "usphone": "", "ukphone": ""}, {"name": "system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "review", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restructure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revamp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "change", "trans": [], "usphone": "", "ukphone": ""}, {"name": "storage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yoga", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recreation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "space", "trans": [], "usphone": "", "ukphone": ""}, {"name": "staff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uncertainty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resistant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "positive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traditional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "benefit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "borrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deduction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "income", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gradually", "trans": [], "usphone": "", "ukphone": ""}, {"name": "owe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "average", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reasonably", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "run", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "significantly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "housing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "option", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alternative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "possibility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drastic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "priority", "trans": [], "usphone": "", "ukphone": ""}, {"name": "push", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compensation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "package", "trans": [], "usphone": "", "ukphone": ""}, {"name": "include", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subsidiary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "update", "trans": [], "usphone": "", "ukphone": ""}, {"name": "merge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "market", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "controller", "trans": [], "usphone": "", "ukphone": ""}, {"name": "candidate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "increase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "news", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "free", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decrease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "step", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "summary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "point", "trans": [], "usphone": "", "ukphone": ""}, {"name": "main", "trans": [], "usphone": "", "ukphone": ""}, {"name": "however", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acquisition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conclusion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "continent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "headquarters", "trans": [], "usphone": "", "ukphone": ""}, {"name": "particularly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steady", "trans": [], "usphone": "", "ukphone": ""}, {"name": "setback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dilemma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "challenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "predicament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obstacle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "result", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaluate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "analysis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "downside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "benefit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advantage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drawback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unacceptable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "miss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sympathetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strength", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weakness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "threat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opportunity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supportive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "satisfaction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wonder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "against", "trans": [], "usphone": "", "ukphone": ""}, {"name": "realize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "atmosphere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "survey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proactive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "role", "trans": [], "usphone": "", "ukphone": ""}, {"name": "submit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "annual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "objective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "development", "trans": [], "usphone": "", "ukphone": ""}, {"name": "play", "trans": [], "usphone": "", "ukphone": ""}, {"name": "involved", "trans": [], "usphone": "", "ukphone": ""}, {"name": "become", "trans": [], "usphone": "", "ukphone": ""}, {"name": "responsibility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specific", "trans": [], "usphone": "", "ukphone": ""}, {"name": "realistic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attainable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consistent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ambitious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "achieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "career", "trans": [], "usphone": "", "ukphone": ""}, {"name": "goal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "set", "trans": [], "usphone": "", "ukphone": ""}]