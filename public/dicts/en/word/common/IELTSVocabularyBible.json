[{"name": "hydrosphere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lithosphere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oxygen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oxide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carbon dioxide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hydrogen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "core", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mantle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "longitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "latitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horizon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "altitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disaster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mishap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catastrophic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calamity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "endanger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jeopardise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destructive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "El Niño", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greenhouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phenomenon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pebble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magnet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mineral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quartz", "trans": [], "usphone": "", "ukphone": ""}, {"name": "granite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "breeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monsoon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hurricane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tornado", "trans": [], "usphone": "", "ukphone": ""}, {"name": "typhoon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volcano", "trans": [], "usphone": "", "ukphone": ""}, {"name": "erupt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thermodynamic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smog", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tsunami", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drought", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flooding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "torrent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "earthquake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seismic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "avalanche", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landscape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "continent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cliff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glacier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swamp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delta", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plateau", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oasis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "globe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hemisphere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arctic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Antarctic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pole", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "axis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deteriorate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aggravate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "degrade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upgrade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "erode", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Mediterranean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Atlantic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pacific", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ocean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "navigation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gulf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "current", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stream", "trans": [], "usphone": "", "ukphone": ""}, {"name": "source", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shallow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "superficial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smooth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sandy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vertical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parallel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "narrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Oceania", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mainland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peninsula", "trans": [], "usphone": "", "ukphone": ""}, {"name": "climate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meteorology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mild", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moderate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "warm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thermal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tropics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "damp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snowy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thaw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frigid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tremble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shiver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thunder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lightning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stormy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "downpour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rainfall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sprinkle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rainbow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "<PERSON><PERSON><PERSON>", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temperature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forecast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mount", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mountain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "range", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ridge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valley", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hillside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overlook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "southern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "southeast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "southwest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "northeast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "northwest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eastern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oriental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inevitable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irreversible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irregularly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inappropriate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abnormal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sediment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "silt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "muddy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dirt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suburb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outskirts", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desolate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adjacent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toxic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pollution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pollutant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contaminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "geology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "border", "trans": [], "usphone": "", "ukphone": ""}, {"name": "margin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fringe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debris", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "splendid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magnificent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "super", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interesting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dramatic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wilderness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deforest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barren", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fertile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fertilise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lunar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calendar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunrise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eclipse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dusk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heaven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paradise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunshine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shadow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vapour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaporate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precipitate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reservoir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "waterfall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fountain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "puff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gush", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intensity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intensive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emerge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "float", "trans": [], "usphone": "", "ukphone": ""}, {"name": "environment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surrounding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "condition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "situation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "natural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "artificial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "synthetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "petrol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gasoline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "petroleum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "photosynthesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dioxide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vegetation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "herb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perennial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "botany", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ecology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ecosystem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eco-friendly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horticulture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "genetics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mutation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "variation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diversity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hybridisation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "classify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reproduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evolve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fluctuate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reclaim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cultivate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harvest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pluck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yield", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bundle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bunch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sunlight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "short-day", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shade-tolerant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fungus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mould", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pollen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "germinate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "burgeon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bud", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blossom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bloom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aromatic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ripen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fruit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wither", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decompose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rainforest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jungle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plantation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "field", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "timber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charcoal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "log", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forestry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "branch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "root", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "straw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thorn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meadow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lawn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "olive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "violet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tulip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reef", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alga", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enzyme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catalyst", "trans": [], "usphone": "", "ukphone": ""}, {"name": "release", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "absorb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circulation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exceed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uptake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nutrient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "energy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surroundings", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mechanism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "counterbalance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preserve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conservation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bush fire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extinguish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destruct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ruin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demolish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "infringe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undermine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extinction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pattern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outcome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seasonal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experimental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "favourable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "productive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "efficient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "considerable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "massive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "immense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maximal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minimal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optimal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "biologist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zoologist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ecologist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "botanist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mammal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vertebrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reptile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amphibian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carnivore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "herbivore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "creature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wildlife", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fauna", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flora", "trans": [], "usphone": "", "ukphone": ""}, {"name": "species", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "herd", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swarm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "throng", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crowd", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cruel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "originate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "derive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ancestor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "descendant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "offspring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subgroup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "breed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interbreed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hybridise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proliferate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sterility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "courtship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hatch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spawn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "claw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bristle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parasite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spider", "trans": [], "usphone": "", "ukphone": ""}, {"name": "butterfly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mosquito", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cricket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penguin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tortoise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turtle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kangaroo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "camel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "panda", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elephant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ivory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wolf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dragon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fox", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cub", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lamb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cattle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ox", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bull", "trans": [], "usphone": "", "ukphone": ""}, {"name": "buffalo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zebra", "trans": [], "usphone": "", "ukphone": ""}, {"name": "donkey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saddle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "falcon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hawk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eagle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "owl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swallow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sparrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pigeon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "goose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "squirrel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frog", "trans": [], "usphone": "", "ukphone": ""}, {"name": "behaviour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "roar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rub", "trans": [], "usphone": "", "ukphone": ""}, {"name": "creep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crawl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "habitat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hedge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barrier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anatomy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "epidemic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gene", "trans": [], "usphone": "", "ukphone": ""}, {"name": "germ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bacteria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "virus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microbe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "metabolism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protein", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vitamin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secrete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excrete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "devour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instinct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intuitive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "potential", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intelligence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "functional", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sensitive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flexible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acoustic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nocturnal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dormant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hibernation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "track", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alternate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "predator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "victim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "captive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defensive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undergo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suffer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vulnerable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subsistence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exterminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tame", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keeper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shepherd", "trans": [], "usphone": "", "ukphone": ""}, {"name": "galaxy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cosmos", "trans": [], "usphone": "", "ukphone": ""}, {"name": "universe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interstellar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrestrial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "celestial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "astronomy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "astrology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "astronaut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meteorite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "envelope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spacecraft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spaceship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "probe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "module", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propulsion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pressure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dynamics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exploration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expedition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flyby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "observatory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "telescope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spectacle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orbit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ecliptic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diameter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radius", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "composition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fossil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sample", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specimen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "particle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "molecule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "atom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electron", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quantum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liquid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fluid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "synthesise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "method", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spectrum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dimension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frequency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "signal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antenna", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circuit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refraction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ultraviolet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radioactive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distinct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discernible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invisible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "squash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fragment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cataclysmic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overwhelming", "trans": [], "usphone": "", "ukphone": ""}, {"name": "despair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desperate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hopeless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "education", "trans": [], "usphone": "", "ukphone": ""}, {"name": "primary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "university", "trans": [], "usphone": "", "ukphone": ""}, {"name": "college", "trans": [], "usphone": "", "ukphone": ""}, {"name": "institute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "academy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "learn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "study", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acquire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knowledge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expertise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "novice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recruit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "literate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "illiteracy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "numerate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "problem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "issue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "controversial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "puzzle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "riddle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obscure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emphasise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enhance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inspire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motivate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stimulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impetus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indulge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spoil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intelligent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "all-round", "trans": [], "usphone": "", "ukphone": ""}, {"name": "genius", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "idiot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wisdom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aptitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excellent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outstanding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brilliant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prestige", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reputation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eminent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "notorious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "esteem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diligent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "painstaking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "approach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scheme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "headmaster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "principal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "faculty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "professor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scholar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scientist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mentor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tutor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lecturer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assistant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "candidate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "degree", "trans": [], "usphone": "", "ukphone": ""}, {"name": "qualify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "certify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "license", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diploma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diplomat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ambassador", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pupil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graduate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ceremony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bachelor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "master", "trans": [], "usphone": "", "ukphone": ""}, {"name": "doctor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fresher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sophomore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "junior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "senior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alumni", "trans": [], "usphone": "", "ukphone": ""}, {"name": "campus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orientation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "platform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "register", "trans": [], "usphone": "", "ukphone": ""}, {"name": "roster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enrol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "matriculation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accommodation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dorm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dining hall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canteen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laboratory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experiment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "data", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quantity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "library", "trans": [], "usphone": "", "ukphone": ""}, {"name": "literature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "article", "trans": [], "usphone": "", "ukphone": ""}, {"name": "author", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fiction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "story", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "poetry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magazine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "journal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coverage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bibliography", "trans": [], "usphone": "", "ukphone": ""}, {"name": "encyclopedia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "biography", "trans": [], "usphone": "", "ukphone": ""}, {"name": "documentary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "series", "trans": [], "usphone": "", "ukphone": ""}, {"name": "record", "trans": [], "usphone": "", "ukphone": ""}, {"name": "file", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "draft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sketch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brochure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frame", "trans": [], "usphone": "", "ukphone": ""}, {"name": "index", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catalogue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "category", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inventory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "content", "trans": [], "usphone": "", "ukphone": ""}, {"name": "context", "trans": [], "usphone": "", "ukphone": ""}, {"name": "list", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chapter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subject", "trans": [], "usphone": "", "ukphone": ""}, {"name": "object", "trans": [], "usphone": "", "ukphone": ""}, {"name": "major", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sociology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "politics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "economics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marketing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accounting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "audit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "psychology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "philosophy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "biology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "physics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chemistry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agriculture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "geography", "trans": [], "usphone": "", "ukphone": ""}, {"name": "history", "trans": [], "usphone": "", "ukphone": ""}, {"name": "engineering", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mechanics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "electronics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maths", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arithmetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "geometry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "algebra", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calculus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "total", "trans": [], "usphone": "", "ukphone": ""}, {"name": "merger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "identical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subtract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multiply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "divide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dividend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remainder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rational", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parameter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "variable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "even", "trans": [], "usphone": "", "ukphone": ""}, {"name": "odd", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "double", "trans": [], "usphone": "", "ukphone": ""}, {"name": "triple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quadruple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multiple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maximum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minimum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "approximately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "graph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diagram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "table", "trans": [], "usphone": "", "ukphone": ""}, {"name": "matrix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rectangle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cube", "trans": [], "usphone": "", "ukphone": ""}, {"name": "angle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "triangle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diagonal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "straight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "round", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sphere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "width", "trans": [], "usphone": "", "ukphone": ""}, {"name": "length", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decimal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "per cent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proportion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ratio", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fraction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ounce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "density", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Fahrenheit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mercury", "trans": [], "usphone": "", "ukphone": ""}, {"name": "battery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transparent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hollow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ozone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gravity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "friction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eccentric", "trans": [], "usphone": "", "ukphone": ""}, {"name": "displace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "melt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dissolve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ferment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dilute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "noxious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "static", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inherent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "formula", "trans": [], "usphone": "", "ukphone": ""}, {"name": "component", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mixture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "theory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "empirical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "practical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "doctrine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "principle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discipline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "term", "trans": [], "usphone": "", "ukphone": ""}, {"name": "semester", "trans": [], "usphone": "", "ukphone": ""}, {"name": "timetable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "schedule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deadline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "course", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lesson", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curriculum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seminar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syllabus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "system", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rudimentary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fundamental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elementary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "superficial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surface", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compulsory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prerequisite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "selective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assignment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "submit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "review", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inspect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consult", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scrutinise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dictate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "examination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "test", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quiz", "trans": [], "usphone": "", "ukphone": ""}, {"name": "presentation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plagiarise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "print", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "essay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dissertation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "project", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heading", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "point", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opinion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "introduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elicit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abstract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "summary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "presume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suppose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hypothesis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "postulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "speculate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "predict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perceive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recognise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conscious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reckon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "imply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deliberate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "represent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "persist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "understand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comprehend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "analyse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diagnose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "infer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conclude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "analogy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contrast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overlap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contradiction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disagree", "trans": [], "usphone": "", "ukphone": ""}, {"name": "differ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nuance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inductive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thorough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "example", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confirm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demonstrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "illustrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manifest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prove", "trans": [], "usphone": "", "ukphone": ""}, {"name": "determine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resolve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "survey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "research", "trans": [], "usphone": "", "ukphone": ""}, {"name": "observe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inquire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "query", "trans": [], "usphone": "", "ukphone": ""}, {"name": "questionnaire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "achieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accomplish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "credit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "score", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rank", "trans": [], "usphone": "", "ukphone": ""}, {"name": "row", "trans": [], "usphone": "", "ukphone": ""}, {"name": "queue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "praise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appreciate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feedback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underestimate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overestimate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fellowship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scholarship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "award", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "technology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "technique", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polytechnic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "engineer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mechanic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "innovate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "breakthrough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gizmo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "devise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disclose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reveal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uncover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "domain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "field", "trans": [], "usphone": "", "ukphone": ""}, {"name": "realm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foundation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specialise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "absorb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concentrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "focus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utilise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "usage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tester", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "device", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appliance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "facility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equipment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instrument", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gauge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "measure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "calculate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "count", "trans": [], "usphone": "", "ukphone": ""}, {"name": "estimate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evaluate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accessory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "byproduct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auxiliary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "versatile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "add", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accumulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assemble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "belong", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microscope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lens", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "echo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sensor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "multimedia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "network", "trans": [], "usphone": "", "ukphone": ""}, {"name": "browser", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microcomputer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laptop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "software", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keyboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "screen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loudspeaker", "trans": [], "usphone": "", "ukphone": ""}, {"name": "microphone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cassette", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "binary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "digital", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wireless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "high-definition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "audio", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fantasy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "science fiction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pivot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hydraulic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drainage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sewage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ventilation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "condense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "simplify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "filter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mode", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mould", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prototype", "trans": [], "usphone": "", "ukphone": ""}, {"name": "framework", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aspect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "facilitate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adapt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adjust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pinpoint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accurate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "error", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mistake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flaw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrong", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fault", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stumble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contingency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circumstance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "culture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "civilisation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "renaissance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "epic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ideology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tradition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "custom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feudalism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slavery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ethical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tribe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aboriginal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inhabitant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "native", "trans": [], "usphone": "", "ukphone": ""}, {"name": "local", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exotic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foreigner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alien", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anthropologist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humanitarian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heritage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inherit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antique", "trans": [], "usphone": "", "ukphone": ""}, {"name": "archaeology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excavate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "museum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pottery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "engrave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decorate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "religion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ritual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "etiquette", "trans": [], "usphone": "", "ukphone": ""}, {"name": "belief", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soul", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spirit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sacred", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hallowed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "holy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "<PERSON>", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bishop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "missionary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "priest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Bible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "church", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cathedral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "choir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pagoda", "trans": [], "usphone": "", "ukphone": ""}, {"name": "empire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "imperial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "royal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dynasty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chronology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emperor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "king", "trans": [], "usphone": "", "ukphone": ""}, {"name": "queen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prince", "trans": [], "usphone": "", "ukphone": ""}, {"name": "princess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "majesty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nobility", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lord", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guardian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nostalgia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "homesick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "celebrity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "status", "trans": [], "usphone": "", "ukphone": ""}, {"name": "background", "trans": [], "usphone": "", "ukphone": ""}, {"name": "experience", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anecdote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accident", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incident", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thrive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prosperity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "setback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adversity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "language", "trans": [], "usphone": "", "ukphone": ""}, {"name": "symbol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gesture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handwriting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pictograph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wedge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "linguistics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "semantic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syntax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grammar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phonetics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pronounce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intonation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inflection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dialect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utterance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "verbal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "syllable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phoneme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vowel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consonant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alphabet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "logogram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vocabulary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dictionary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "idiom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phrase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expression", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "root", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prefix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suffix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abbreviation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "synonym", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antonym", "trans": [], "usphone": "", "ukphone": ""}, {"name": "noun", "trans": [], "usphone": "", "ukphone": ""}, {"name": "singular", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pronoun", "trans": [], "usphone": "", "ukphone": ""}, {"name": "verb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adjective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adverb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preposition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conjunction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consistent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complicated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "version", "trans": [], "usphone": "", "ukphone": ""}, {"name": "translate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paraphrase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interpret", "trans": [], "usphone": "", "ukphone": ""}, {"name": "narrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "illuminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decipher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eloquence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "communicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discussion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brainstorm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commentary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negotiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "press", "trans": [], "usphone": "", "ukphone": ""}, {"name": "journalist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "critic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commentator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exponent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "announcer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correspondent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "messenger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "editor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "typist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leaflet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propaganda", "trans": [], "usphone": "", "ukphone": ""}, {"name": "publish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disseminate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foresee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anticipate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "await", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pastime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entertain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recreation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gossip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rumour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consensus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "festival", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "programme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rehearsal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "imitate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mimic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "simulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "circus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drama", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "symphony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orchestra", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ballet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opera", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tragedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "animation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "film", "trans": [], "usphone": "", "ukphone": ""}, {"name": "movie", "trans": [], "usphone": "", "ukphone": ""}, {"name": "X-rated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "artist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "craftsman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "painter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "role", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scene", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gallery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhibition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aesthetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "select", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "photograph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "portrait", "trans": [], "usphone": "", "ukphone": ""}, {"name": "painting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sculpture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "draw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sketch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "describe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "improvise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "musical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "classical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jazz", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hip-hop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lyric", "trans": [], "usphone": "", "ukphone": ""}, {"name": "band", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "melody", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rhythm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tune", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disc", "trans": [], "usphone": "", "ukphone": ""}, {"name": "piano", "trans": [], "usphone": "", "ukphone": ""}, {"name": "violin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cello", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guitar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harmonica", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trumpet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "competition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tournament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Olympic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sponsor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patron", "trans": [], "usphone": "", "ukphone": ""}, {"name": "athlete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "champion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spectator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volunteer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "famous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "well-known", "trans": [], "usphone": "", "ukphone": ""}, {"name": "energetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vigorous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stadium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gym", "trans": [], "usphone": "", "ukphone": ""}, {"name": "training", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exercise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indoor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outdoor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mobile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "movement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yoga", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sprawl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stretch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "badminton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "golf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "billiards", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soccer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tennis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volleyball", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hockey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cricket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "goal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "racket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pitch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "throw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tumble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ski", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cycling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plunge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bounce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "camp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "picnic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hunt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "race", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marathon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pedestrian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "step", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excursion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cruise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vacation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jog", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stride", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wander", "trans": [], "usphone": "", "ukphone": ""}, {"name": "linger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "climb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pull", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stuff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "item", "trans": [], "usphone": "", "ukphone": ""}, {"name": "merchandise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "souvenir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "artefact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "material", "trans": [], "usphone": "", "ukphone": ""}, {"name": "raw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "necessity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outfit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "utensil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garbage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rubbish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recycle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "litter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "waste", "trans": [], "usphone": "", "ukphone": ""}, {"name": "junk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landfill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sewerage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detergent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lotion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shampoo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tub", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pipe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tube", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "broom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sweep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mattress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carpet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cushion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blanket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quilt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sheet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pillow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sponge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "towel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "staple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "razor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cord", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "match", "trans": [], "usphone": "", "ukphone": ""}, {"name": "candle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "portfolio", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paperback", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pamphlet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tissue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Xerox", "trans": [], "usphone": "", "ukphone": ""}, {"name": "duplicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "memorandum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stationery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rubber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scissors", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "edge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "element", "trans": [], "usphone": "", "ukphone": ""}, {"name": "factor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "section", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "label", "trans": [], "usphone": "", "ukphone": ""}, {"name": "badge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bolt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knob", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shutter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curtain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opacity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barrel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bucket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "phone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "camera", "trans": [], "usphone": "", "ukphone": ""}, {"name": "portable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spotlight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lantern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bulb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flashlight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refrigerator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fridge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vacuum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "switch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hurdle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pedal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shelf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ladder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drawer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "umbrella", "trans": [], "usphone": "", "ukphone": ""}, {"name": "raincoat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dredge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "can", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alloy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "metal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "iron", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bronze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pitch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plaster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plastic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fibre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fabric", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canvas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "linen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cotton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nylon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lumber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wooden", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fuel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lubricate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diamond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crystal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inferior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "counterfeit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fragile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "miniature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "available", "trans": [], "usphone": "", "ukphone": ""}, {"name": "durable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fashion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "style", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tendency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "current", "trans": [], "usphone": "", "ukphone": ""}, {"name": "popularity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vogue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prevail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "model", "trans": [], "usphone": "", "ukphone": ""}, {"name": "icon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "idol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "luxury", "trans": [], "usphone": "", "ukphone": ""}, {"name": "extravagant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jewellery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jewel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adorn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ornament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "embellish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "embroider", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hairdressing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pigment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dye", "trans": [], "usphone": "", "ukphone": ""}, {"name": "masquerade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "veil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "costume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fascinate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exquisite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elegance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perfect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appearance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cosmetics", "trans": [], "usphone": "", "ukphone": ""}, {"name": "make-up", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handsome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charming", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pretty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beautiful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ugly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clothe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uniform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laundry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wardrobe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overcoat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "robe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sweater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jacket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skirt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jeans", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trousers", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clasp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "button", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glove", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scarf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handkerchief", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wallet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cloak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sleeve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stocking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slipper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tailor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stitch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "needle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "string", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thread", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stripe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ribbon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "belt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bracelet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "necklace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "textile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "velvet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shabby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "colour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "white", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yellow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "figure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "food", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appetite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cater", "trans": [], "usphone": "", "ukphone": ""}, {"name": "provision", "trans": [], "usphone": "", "ukphone": ""}, {"name": "edible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recipe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restaurant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refectory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cafeteria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "buffet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barbecue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "banquet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refreshment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appetiser", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cuisine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "menu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "order", "trans": [], "usphone": "", "ukphone": ""}, {"name": "takeaway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chef", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gourmet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vegetarian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cutlery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "silver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ceramic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "porcelain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bowl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saucer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tray", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fork", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knife", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spoon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kettle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stove", "trans": [], "usphone": "", "ukphone": ""}, {"name": "furnace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oven", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beverage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "juice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soda", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coffee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alcohol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liquor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whisky", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brandy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drunk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tobacco", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cigarette", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sober", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vegetable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tomato", "trans": [], "usphone": "", "ukphone": ""}, {"name": "potato", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cucumber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cabbage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "onion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mushroom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eggplant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carrot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turnip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fruit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "core", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hull", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cherry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "berry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "papaya", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orange", "trans": [], "usphone": "", "ukphone": ""}, {"name": "melon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lemon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kiwi", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wheat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "porridge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paste", "trans": [], "usphone": "", "ukphone": ""}, {"name": "livestock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chicken", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turkey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beef", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pork", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mutton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sausage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rod", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dairy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "milk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yogurt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cream", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheese", "trans": [], "usphone": "", "ukphone": ""}, {"name": "butter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sandwich", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hamburger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loaf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pie", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pizza", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pasta", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spaghetti", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pudding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "biscuit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jam", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chocolate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ice cream", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vanilla", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mustard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wasabi", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pepper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ginger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garlic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scallion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vinegar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sugar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "candy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "honey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flavour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sweet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bitter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spicy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delicious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yummy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tasty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hunger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thirsty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sauce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ketchup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perfume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ingredient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supplement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "digest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "roast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swallow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "squeeze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "architecture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "erection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "structure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "construct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obstruct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "establish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "build", "trans": [], "usphone": "", "ukphone": ""}, {"name": "found", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concrete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stability", "trans": [], "usphone": "", "ukphone": ""}, {"name": "site", "trans": [], "usphone": "", "ukphone": ""}, {"name": "venue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landmark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "situated", "trans": [], "usphone": "", "ukphone": ""}, {"name": "locate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inhabit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "migrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "settle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dwelling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skyscraper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "villa", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mansion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apartment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hostel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lodge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cabin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cellar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cottage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nursery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cradle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shelter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "block", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aisle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "porch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corridor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "staircase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "storey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "layer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elevator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "escalator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "handrail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pillar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "column", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beam", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vault", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ceiling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eaves", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chimney", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reception", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lobby", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bench", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parlour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fireplace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radiator", "trans": [], "usphone": "", "ukphone": ""}, {"name": "living room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cabinet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "balcony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kitchen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lavatory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toilet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bath", "trans": [], "usphone": "", "ukphone": ""}, {"name": "basin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reserve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "store", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supermarket", "trans": [], "usphone": "", "ukphone": ""}, {"name": "booth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "casino", "trans": [], "usphone": "", "ukphone": ""}, {"name": "studio", "trans": [], "usphone": "", "ukphone": ""}, {"name": "downtown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "urban", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peripheral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vicinity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "room", "trans": [], "usphone": "", "ukphone": ""}, {"name": "void", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spacious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "airtight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expanse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plaza", "trans": [], "usphone": "", "ukphone": ""}, {"name": "castle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carpenter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mason", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brickwork", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suspension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pole", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scaffold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "infrastructure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apparatus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "malfunction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maintain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fix", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weld", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "screw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plumb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "viaduct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "span", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dam", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assemble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "install", "trans": [], "usphone": "", "ukphone": ""}, {"name": "furnish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "placement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "layout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "design", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entrance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outside", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exterior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "external", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "internal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "navigate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "voyage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aviation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "journey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "travel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safari", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "haunt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attraction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "memorial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pyramid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "port", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visa", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traffic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "airline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "airplane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "helicopter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parachute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pilot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "passenger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "baggage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "luggage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suitcase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carry-on", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "atlas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "route", "trans": [], "usphone": "", "ukphone": ""}, {"name": "itinerary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "passage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intersection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cross", "trans": [], "usphone": "", "ukphone": ""}, {"name": "way", "trans": [], "usphone": "", "ukphone": ""}, {"name": "path", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "avenue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "highway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "signpost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vehicle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auto", "trans": [], "usphone": "", "ukphone": ""}, {"name": "express", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tram", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ambulance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "truck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lorry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "van", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wagon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carriage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tractor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pedicab", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cycle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "garage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "motor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horsepower", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wheel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tyre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "honk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "captain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emergency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wreck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crush", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vanish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disappear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hazard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harbour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anchor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "submerge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sailor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seaman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "carrier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steamer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vessel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ferry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "raft", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canoe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turbine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propeller", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aboard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "embark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "channel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "canal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ditch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "railroad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "railway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "locomotive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "underground", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tunnel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "postpone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lull", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "due", "trans": [], "usphone": "", "ukphone": ""}, {"name": "postage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stamp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "envelope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "packet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "package", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parcel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "load", "trans": [], "usphone": "", "ukphone": ""}, {"name": "burden", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transfer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transmit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deliver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "speed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "velocity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swift", "trans": [], "usphone": "", "ukphone": ""}, {"name": "republic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Marxism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "socialism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "communism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "government", "trans": [], "usphone": "", "ukphone": ""}, {"name": "authority", "trans": [], "usphone": "", "ukphone": ""}, {"name": "political", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hierarchy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "democracy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bureaucracy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "egalitarian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "materialism", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revolution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "process", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conservative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meltdown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "municipal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "neutral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bilateral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arena", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flag", "trans": [], "usphone": "", "ukphone": ""}, {"name": "banner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "president", "trans": [], "usphone": "", "ukphone": ""}, {"name": "premier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minister", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secretary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "parliament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "senate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meeting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "headquarters", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delegation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "behalf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "police", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statesman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mayor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "service", "trans": [], "usphone": "", "ukphone": ""}, {"name": "office", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bureau", "trans": [], "usphone": "", "ukphone": ""}, {"name": "department", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "administration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dominate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "power", "trans": [], "usphone": "", "ukphone": ""}, {"name": "influence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "importance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "significance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organisation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "association", "trans": [], "usphone": "", "ukphone": ""}, {"name": "union", "trans": [], "usphone": "", "ukphone": ""}, {"name": "community", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consortium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "league", "trans": [], "usphone": "", "ukphone": ""}, {"name": "institution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unique", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "global", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worldwide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "federal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foreign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overseas", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abroad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "civil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emigrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "immigrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "puppet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "throne", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wreath", "trans": [], "usphone": "", "ukphone": ""}, {"name": "colony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liberty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "independence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refuge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "asylum", "trans": [], "usphone": "", "ukphone": ""}, {"name": "population", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demographic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "citizen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resident", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ethnic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "racial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "franchise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entitle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preference", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respondent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "poll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ambition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nominate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "checklist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "succession", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safety", "trans": [], "usphone": "", "ukphone": ""}, {"name": "welfare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "well-being", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harmony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steady", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flourish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "succeed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prospect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perspective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "viewpoint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "standpoint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "outlook", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guideline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ethic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suggest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proposal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "declare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affirm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "claim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proclaim", "trans": [], "usphone": "", "ukphone": ""}, {"name": "state", "trans": [], "usphone": "", "ukphone": ""}, {"name": "announce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clarify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "encourage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "implement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "admit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "African", "trans": [], "usphone": "", "ukphone": ""}, {"name": "European", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Latin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Jewish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Arabian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Portuguese", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Roman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Russian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Spanish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Swiss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Greek", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Italian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soviet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Indian", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Australia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "New Zealand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Canada", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Britain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "France", "trans": [], "usphone": "", "ukphone": ""}, {"name": "Germany", "trans": [], "usphone": "", "ukphone": ""}, {"name": "economy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "market", "trans": [], "usphone": "", "ukphone": ""}, {"name": "industry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "merchant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "financial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fund", "trans": [], "usphone": "", "ukphone": ""}, {"name": "donate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "endow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commercial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "advertise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slogan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purchase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "afford", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grocery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discount", "trans": [], "usphone": "", "ukphone": ""}, {"name": "promotion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "voucher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coupon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "currency", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exchange", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "receipt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invoice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "levy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tariff", "trans": [], "usphone": "", "ukphone": ""}, {"name": "duty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revenue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gross", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recession", "trans": [], "usphone": "", "ukphone": ""}, {"name": "depression", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crisis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "security", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "debt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deficit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "inflation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deflation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dwindle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decrease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "downsize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diminish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reduce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "increase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "increment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "growth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "escalate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "influx", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affluent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enrich", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sufficient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adequate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saving", "trans": [], "usphone": "", "ukphone": ""}, {"name": "redundant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "company", "trans": [], "usphone": "", "ukphone": ""}, {"name": "firm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enterprise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "entity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "input", "trans": [], "usphone": "", "ukphone": ""}, {"name": "product", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acquisition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "goods", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cargo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guarantee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reliable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negotiation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "absent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wholesale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "auction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transaction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turnover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "export", "trans": [], "usphone": "", "ukphone": ""}, {"name": "import", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quota", "trans": [], "usphone": "", "ukphone": ""}, {"name": "share", "trans": [], "usphone": "", "ukphone": ""}, {"name": "benefit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dividend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "account", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deposit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instalment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheque", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shilling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treasure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wealth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "estate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "possess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "legacy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "descend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worthy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "valuable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incentive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accelerate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prompt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "income", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "payment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refund", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reimburse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "payroll", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remuneration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subsidy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "earn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undertaking", "trans": [], "usphone": "", "ukphone": ""}, {"name": "career", "trans": [], "usphone": "", "ukphone": ""}, {"name": "profession", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occupation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "job", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vocation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "labour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "workforce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "workaholic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "personnel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clerk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interview", "trans": [], "usphone": "", "ukphone": ""}, {"name": "r<PERSON><PERSON><PERSON>", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recommend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "employ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lay-off", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dismiss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discharge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "result", "trans": [], "usphone": "", "ukphone": ""}, {"name": "failure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abortion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opportunity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "choose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "change", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "develop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sustainable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "improve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sanction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coordinate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cooperate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collaborate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "law", "trans": [], "usphone": "", "ukphone": ""}, {"name": "legal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rule", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regulation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "item", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treaty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agreement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "charter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protocol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "norm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "benchmark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "criteria", "trans": [], "usphone": "", "ukphone": ""}, {"name": "standard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "admission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "perform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enforce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "default", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reject", "trans": [], "usphone": "", "ukphone": ""}, {"name": "constraint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "official", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mandatory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "potent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obligation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "court", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supreme", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "legislate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "just", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prejudice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discrimination", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prosecute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "condemn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "complain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boycott", "trans": [], "usphone": "", "ukphone": ""}, {"name": "session", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mediate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plea", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appeal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contradict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "persuade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "controversy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whisper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mutter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "murmur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "silent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "investigate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "witness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "justify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reasonable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suitable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "judge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jury", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trial", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suspect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sentence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prison", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oversee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supervise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restrict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restrain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bribe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rob", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pirate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thief", "trans": [], "usphone": "", "ukphone": ""}, {"name": "murder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suicide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deceive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fraud", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trick", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pretend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fool", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stigma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conform", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reconcile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obey", "trans": [], "usphone": "", "ukphone": ""}, {"name": "succumb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penalty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mortgage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ownership", "trans": [], "usphone": "", "ukphone": ""}, {"name": "copyright", "trans": [], "usphone": "", "ukphone": ""}, {"name": "privilege", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accredit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "access", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anonymous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "false", "trans": [], "usphone": "", "ukphone": ""}, {"name": "genuine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "procedure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "routine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consequence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "request", "trans": [], "usphone": "", "ukphone": ""}, {"name": "require", "trans": [], "usphone": "", "ukphone": ""}, {"name": "petition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "command", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instruct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "violence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conflict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "raid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enormity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "campaign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "battle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "struggle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explosion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bombard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bomb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "burst", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spark", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twinkle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gunpowder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tank", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cannon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "missile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weapon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pistol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rifle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bullet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "target", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deflect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mission", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flame", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glitter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sword", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hammer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "helmet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shield", "trans": [], "usphone": "", "ukphone": ""}, {"name": "equip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prepare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "provide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deploy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strike", "trans": [], "usphone": "", "ukphone": ""}, {"name": "punch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stab", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dilemma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pitfall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conspiracy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "destitute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "famine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "starve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deprive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seize", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pursue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "threat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "offend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aggressive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trespass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intrude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "violate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assault", "trans": [], "usphone": "", "ukphone": ""}, {"name": "challenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "siege", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surround", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enclose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "halt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oppose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oppress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "counter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forbid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prohibit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prevent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revolt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "betray", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treason", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rebel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "traitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patriot", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blame", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reproach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evidence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proof", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terror", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrific", "trans": [], "usphone": "", "ukphone": ""}, {"name": "horrible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alarm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dread", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frighten", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turbulent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "turmoil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scenario", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occasion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boundary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rein", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rival", "trans": [], "usphone": "", "ukphone": ""}, {"name": "competitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enemy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strategy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tactic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manoeuvre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blueprint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "warn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oblige", "trans": [], "usphone": "", "ukphone": ""}, {"name": "order", "trans": [], "usphone": "", "ukphone": ""}, {"name": "protest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "might", "trans": [], "usphone": "", "ukphone": ""}, {"name": "force", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reinforce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impair", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mitigate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comfort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "event", "trans": [], "usphone": "", "ukphone": ""}, {"name": "matter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "happen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "occur", "trans": [], "usphone": "", "ukphone": ""}, {"name": "savage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reckless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fierce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foul", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "devil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "punishment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "execute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hang", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sacrifice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "casualty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bury", "trans": [], "usphone": "", "ukphone": ""}, {"name": "funeral", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tomb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maritime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "naval", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fleet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "troop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proceed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "progress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "military", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nuclear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "poison", "trans": [], "usphone": "", "ukphone": ""}, {"name": "devastate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "risk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adventure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peril", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catastrophe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collapse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "torture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humiliate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "damage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harmful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mislead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disturb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interfere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "interrupt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retreat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shrink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "withdraw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "withstand", "trans": [], "usphone": "", "ukphone": ""}, {"name": "compromise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surrender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "escape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "avoid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abandon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forgo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defend", "trans": [], "usphone": "", "ukphone": ""}, {"name": "safeguard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "defeat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "capture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "victory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "triumph", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conquer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monument", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tablet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "statue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "honour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glorious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "devote", "trans": [], "usphone": "", "ukphone": ""}, {"name": "owe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patrol", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "camouflage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obstacle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "burrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scout", "trans": [], "usphone": "", "ukphone": ""}, {"name": "general", "trans": [], "usphone": "", "ukphone": ""}, {"name": "soldier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "veteran", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pioneer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prophet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "herald", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heir", "trans": [], "usphone": "", "ukphone": ""}, {"name": "successor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surname", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "female", "trans": [], "usphone": "", "ukphone": ""}, {"name": "couple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "husband", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gay", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grandfather", "trans": [], "usphone": "", "ukphone": ""}, {"name": "father-in-law", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sibling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cousin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nephew", "trans": [], "usphone": "", "ukphone": ""}, {"name": "niece", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "embryo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "infant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "orphan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "juvenile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "youngster", "trans": [], "usphone": "", "ukphone": ""}, {"name": "youth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gang", "trans": [], "usphone": "", "ukphone": ""}, {"name": "teenager", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adolescence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "folk", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intimate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "darling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beloved", "trans": [], "usphone": "", "ukphone": ""}, {"name": "madam", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hostess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landlady", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mistress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "housewife", "trans": [], "usphone": "", "ukphone": ""}, {"name": "widow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "maiden", "trans": [], "usphone": "", "ukphone": ""}, {"name": "household", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "host", "trans": [], "usphone": "", "ukphone": ""}, {"name": "landlord", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tenant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "customer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "client", "trans": [], "usphone": "", "ukphone": ""}, {"name": "encounter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "miss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "appointment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accompany", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dependent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "engagement", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wedding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "honeymoon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kiss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "divorce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "separate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "single", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sole", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "each", "trans": [], "usphone": "", "ukphone": ""}, {"name": "individual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commuter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "neighbourhood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "member", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manager", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supervisor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "subordinate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "partner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "colleague", "trans": [], "usphone": "", "ukphone": ""}, {"name": "comrade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acquaintance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "friendship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "affection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stranger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apprentice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deputy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hero", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heroine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "actress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chancellor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commander", "trans": [], "usphone": "", "ukphone": ""}, {"name": "director", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proponent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "committee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "council", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delegate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specialist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consultant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "veterinary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "counsellor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "solicitor", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attorney", "trans": [], "usphone": "", "ukphone": ""}, {"name": "umpire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cashier", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fireman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nurse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gardener", "trans": [], "usphone": "", "ukphone": ""}, {"name": "barber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "haircut", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fisherman", "trans": [], "usphone": "", "ukphone": ""}, {"name": "butcher", "trans": [], "usphone": "", "ukphone": ""}, {"name": "groom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hostage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beggar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "coward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "act", "trans": [], "usphone": "", "ukphone": ""}, {"name": "behave", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accustom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "react", "trans": [], "usphone": "", "ukphone": ""}, {"name": "respond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reflect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adopt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nourish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "support", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhale", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "survive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glimpse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "glance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gaze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contemplate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oath", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pledge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whistle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "notify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quarrel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dispute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "argument", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mention", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hurry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hasten", "trans": [], "usphone": "", "ukphone": ""}, {"name": "urge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "provoke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "boast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mock", "trans": [], "usphone": "", "ukphone": ""}, {"name": "touch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "applaud", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kneel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "catch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "snatch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grab", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grasp", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overtake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "follow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "twist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scatter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fasten", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loosen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scratch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wipe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scrape", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "peel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "split", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sway", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vibrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "whirl", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rotate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shuffle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "collide", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contact", "trans": [], "usphone": "", "ukphone": ""}, {"name": "connect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "combine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "integrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penetrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pierce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tilt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trail", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arrange", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manipulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "baptise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gamble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "promise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attract", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obsess", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mesmerise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nightmare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amaze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "marvel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "astonish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "involve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "annoy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "upset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bewilder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indignity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contempt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "neglect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disregard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ignorance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bias", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deviate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "expel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bully", "trans": [], "usphone": "", "ukphone": ""}, {"name": "panic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "terrify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "revenge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kidnap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hijack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "smuggle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strangle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "massacre", "trans": [], "usphone": "", "ukphone": ""}, {"name": "decimation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intervene", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impede", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bait", "trans": [], "usphone": "", "ukphone": ""}, {"name": "induce", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tempt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "designate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assign", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "despatch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undo", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disguise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conceal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exclude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "undertake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stipulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "convince", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reassure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aspire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yearn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invoke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "itch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attempt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "effort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fulfil", "trans": [], "usphone": "", "ukphone": ""}, {"name": "range", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "loom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "launch", "trans": [], "usphone": "", "ukphone": ""}, {"name": "commence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exploit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "explore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exert", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tackle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cope", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dispose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "conduct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "omit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delete", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cancel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "erase", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rescue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recover", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rectify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "redeem", "trans": [], "usphone": "", "ukphone": ""}, {"name": "offset", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replenish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obtain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "supply", "trans": [], "usphone": "", "ukphone": ""}, {"name": "offer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "render", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enlarge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "augment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "magnify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amplify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exaggerate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prolong", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uphold", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backup", "trans": [], "usphone": "", "ukphone": ""}, {"name": "propel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "update", "trans": [], "usphone": "", "ukphone": ""}, {"name": "raise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "leak", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ooze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "evacuate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trench", "trans": [], "usphone": "", "ukphone": ""}, {"name": "saturate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forgive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flatter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "worship", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cause", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "purpose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "objective", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arouse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trigger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seek", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retrieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "testify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "verify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "specify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enlighten", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impart", "trans": [], "usphone": "", "ukphone": ""}, {"name": "deserve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drop", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sink", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plummet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "muffle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overshadow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "welcome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "salute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "celebrate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "congratulate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "participate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "farewell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "assort", "trans": [], "usphone": "", "ukphone": ""}, {"name": "correspond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "accord", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attribute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recall", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recollect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repeat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "retrospect", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surmount", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mortify", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pervade", "trans": [], "usphone": "", "ukphone": ""}, {"name": "replace", "trans": [], "usphone": "", "ukphone": ""}, {"name": "substitute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distinguish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "differentiate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incline", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sideways", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mood", "trans": [], "usphone": "", "ukphone": ""}, {"name": "emotion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temper", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manner", "trans": [], "usphone": "", "ukphone": ""}, {"name": "attitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "character", "trans": [], "usphone": "", "ukphone": ""}, {"name": "personality", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trait", "trans": [], "usphone": "", "ukphone": ""}, {"name": "virtue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stature", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flesh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mankind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "human", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "backbone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skeleton", "trans": [], "usphone": "", "ukphone": ""}, {"name": "skull", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "physical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheek", "trans": [], "usphone": "", "ukphone": ""}, {"name": "forehead", "trans": [], "usphone": "", "ukphone": ""}, {"name": "brow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eyelash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lip", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mouth", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tongue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "throat", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gorge", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jaw", "trans": [], "usphone": "", "ukphone": ""}, {"name": "neck", "trans": [], "usphone": "", "ukphone": ""}, {"name": "beard", "trans": [], "usphone": "", "ukphone": ""}, {"name": "shoulder", "trans": [], "usphone": "", "ukphone": ""}, {"name": "limb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "elbow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wrist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thumb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "palm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "abdomen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rib", "trans": [], "usphone": "", "ukphone": ""}, {"name": "waist", "trans": [], "usphone": "", "ukphone": ""}, {"name": "organ", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liver", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stomach", "trans": [], "usphone": "", "ukphone": ""}, {"name": "womb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "kidney", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lung", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gland", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chamber", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intestine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thigh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "joint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "knee", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ankle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heel", "trans": [], "usphone": "", "ukphone": ""}, {"name": "toe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "muscle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pulse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "artery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nerve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hormone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "immune", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stimulus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "biorhythm", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lifestyle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "habit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sense", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eyesight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "view", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "visible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vague", "trans": [], "usphone": "", "ukphone": ""}, {"name": "blind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dumb", "trans": [], "usphone": "", "ukphone": ""}, {"name": "aural", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dental", "trans": [], "usphone": "", "ukphone": ""}, {"name": "awake", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yawn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nap", "trans": [], "usphone": "", "ukphone": ""}, {"name": "doze", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quiet", "trans": [], "usphone": "", "ukphone": ""}, {"name": "asleep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cripple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lame", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dwarf", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pregnancy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "born", "trans": [], "usphone": "", "ukphone": ""}, {"name": "condom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clone", "trans": [], "usphone": "", "ukphone": ""}, {"name": "symptom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "illness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moan", "trans": [], "usphone": "", "ukphone": ""}, {"name": "infection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "detriment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "broken", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "invalid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "patient", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dysfunction", "trans": [], "usphone": "", "ukphone": ""}, {"name": "diabetes", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obesity", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overweight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hypertension", "trans": [], "usphone": "", "ukphone": ""}, {"name": "paralyse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dizzy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insomnia", "trans": [], "usphone": "", "ukphone": ""}, {"name": "allergy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heart attack", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cancer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arthritis", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pimple", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stroke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "plague", "trans": [], "usphone": "", "ukphone": ""}, {"name": "flu", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fever", "trans": [], "usphone": "", "ukphone": ""}, {"name": "headache", "trans": [], "usphone": "", "ukphone": ""}, {"name": "swell", "trans": [], "usphone": "", "ukphone": ""}, {"name": "choke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "injure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "injury", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hurt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bleed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bruise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "scar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trauma", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lump", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pain", "trans": [], "usphone": "", "ukphone": ""}, {"name": "death", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mortal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "demise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "doom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fatal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lethal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "corpus", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overwork", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhaust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fatigue", "trans": [], "usphone": "", "ukphone": ""}, {"name": "faint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "feeble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weaken", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uncomfortable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unconscious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "chronic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sorrow", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suffering", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lament", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hospital", "trans": [], "usphone": "", "ukphone": ""}, {"name": "therapy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "health care", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sanitary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hygiene", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fitness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clinic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "physician", "trans": [], "usphone": "", "ukphone": ""}, {"name": "surgeon", "trans": [], "usphone": "", "ukphone": ""}, {"name": "operation", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "heal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "treatment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "check", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prescription", "trans": [], "usphone": "", "ukphone": ""}, {"name": "transplant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "X-ray", "trans": [], "usphone": "", "ukphone": ""}, {"name": "injection", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precaution", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vaccinate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quarantine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "segregate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "isolate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pharmacy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medicine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "drug", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "antibiotic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "acupuncture", "trans": [], "usphone": "", "ukphone": ""}, {"name": "penicillin", "trans": [], "usphone": "", "ukphone": ""}, {"name": "morphine", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dose", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recovery", "trans": [], "usphone": "", "ukphone": ""}, {"name": "refresh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relief", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "alleviate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resume", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ease", "trans": [], "usphone": "", "ukphone": ""}, {"name": "relax", "trans": [], "usphone": "", "ukphone": ""}, {"name": "normal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "common", "trans": [], "usphone": "", "ukphone": ""}, {"name": "usual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "optimistic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pessimistic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "positive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negative", "trans": [], "usphone": "", "ukphone": ""}, {"name": "passive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enthusiastic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indifferent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apathetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "negligible", "trans": [], "usphone": "", "ukphone": ""}, {"name": "happiness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "merry", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fun", "trans": [], "usphone": "", "ukphone": ""}, {"name": "joke", "trans": [], "usphone": "", "ukphone": ""}, {"name": "laughter", "trans": [], "usphone": "", "ukphone": ""}, {"name": "joy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "please", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rejoice", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thrill", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cheer", "trans": [], "usphone": "", "ukphone": ""}, {"name": "pleasure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agreeable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exhilarate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "satisfactory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "desirable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "exciting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "keen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "zeal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lively", "trans": [], "usphone": "", "ukphone": ""}, {"name": "lovely", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fond", "trans": [], "usphone": "", "ukphone": ""}, {"name": "favour", "trans": [], "usphone": "", "ukphone": ""}, {"name": "startle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "amazing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "astound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hectic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liberal", "trans": [], "usphone": "", "ukphone": ""}, {"name": "generous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gentle", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "humorous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "polite", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frank", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mysterious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "curious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "daring", "trans": [], "usphone": "", "ukphone": ""}, {"name": "manly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "direct", "trans": [], "usphone": "", "ukphone": ""}, {"name": "earnest", "trans": [], "usphone": "", "ukphone": ""}, {"name": "honesty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "proud", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rational", "trans": [], "usphone": "", "ukphone": ""}, {"name": "seriously", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "friendly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hospitable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "romantic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "freedom", "trans": [], "usphone": "", "ukphone": ""}, {"name": "careful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "concern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confidence", "trans": [], "usphone": "", "ukphone": ""}, {"name": "considerate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "thoughtful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sympathetic", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ready", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apologise", "trans": [], "usphone": "", "ukphone": ""}, {"name": "apology", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mercy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "admire", "trans": [], "usphone": "", "ukphone": ""}, {"name": "remarkable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grateful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gratitude", "trans": [], "usphone": "", "ukphone": ""}, {"name": "eager", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sound", "trans": [], "usphone": "", "ukphone": ""}, {"name": "picturesque", "trans": [], "usphone": "", "ukphone": ""}, {"name": "promising", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prominent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "awesome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arduous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tough", "trans": [], "usphone": "", "ukphone": ""}, {"name": "robust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sturdy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "steadfast", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tender", "trans": [], "usphone": "", "ukphone": ""}, {"name": "temperate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "delicate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mundane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "naive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "childish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sincere", "trans": [], "usphone": "", "ukphone": ""}, {"name": "liable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "trustworthy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "enterprising", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sane", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strenuous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rigorous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "strict", "trans": [], "usphone": "", "ukphone": ""}, {"name": "severe", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mutual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "resemble", "trans": [], "usphone": "", "ukphone": ""}, {"name": "similar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "familiar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "naked", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bare", "trans": [], "usphone": "", "ukphone": ""}, {"name": "private", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intuition", "trans": [], "usphone": "", "ukphone": ""}, {"name": "spontaneous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "implicit", "trans": [], "usphone": "", "ukphone": ""}, {"name": "illusion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "imaginary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fancy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fortune", "trans": [], "usphone": "", "ukphone": ""}, {"name": "agony", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grief", "trans": [], "usphone": "", "ukphone": ""}, {"name": "grieve", "trans": [], "usphone": "", "ukphone": ""}, {"name": "miserable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "distress", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sadness", "trans": [], "usphone": "", "ukphone": ""}, {"name": "gloomy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disappoint", "trans": [], "usphone": "", "ukphone": ""}, {"name": "discourage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wretched", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frustrating", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mourn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anger", "trans": [], "usphone": "", "ukphone": ""}, {"name": "furious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "frown", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rage", "trans": [], "usphone": "", "ukphone": ""}, {"name": "irritate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hatred", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nuisance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "disgust", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vex", "trans": [], "usphone": "", "ukphone": ""}, {"name": "harass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bore", "trans": [], "usphone": "", "ukphone": ""}, {"name": "bother", "trans": [], "usphone": "", "ukphone": ""}, {"name": "troublesome", "trans": [], "usphone": "", "ukphone": ""}, {"name": "awful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unpleasant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "adverse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hostile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "indignant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "radical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mad", "trans": [], "usphone": "", "ukphone": ""}, {"name": "crazy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "wicked", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vicious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "vulgar", "trans": [], "usphone": "", "ukphone": ""}, {"name": "mean", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ego", "trans": [], "usphone": "", "ukphone": ""}, {"name": "selfish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "nasty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rash", "trans": [], "usphone": "", "ukphone": ""}, {"name": "volatile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weird", "trans": [], "usphone": "", "ukphone": ""}, {"name": "excessive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anxiety", "trans": [], "usphone": "", "ukphone": ""}, {"name": "restless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "uneasy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fear", "trans": [], "usphone": "", "ukphone": ""}, {"name": "timid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "fuss", "trans": [], "usphone": "", "ukphone": ""}, {"name": "insult", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unkind", "trans": [], "usphone": "", "ukphone": ""}, {"name": "envy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "jealous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "doubt", "trans": [], "usphone": "", "ukphone": ""}, {"name": "suspicion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sceptical", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unsatisfactory", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unstable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unsuitable", "trans": [], "usphone": "", "ukphone": ""}, {"name": "unwilling", "trans": [], "usphone": "", "ukphone": ""}, {"name": "reluctant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "hesitate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "confuse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "guilty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "embarrass", "trans": [], "usphone": "", "ukphone": ""}, {"name": "regret", "trans": [], "usphone": "", "ukphone": ""}, {"name": "repent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ashamed", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sigh", "trans": [], "usphone": "", "ukphone": ""}, {"name": "weep", "trans": [], "usphone": "", "ukphone": ""}, {"name": "tolerance", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ignorant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "endure", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "cunning", "trans": [], "usphone": "", "ukphone": ""}, {"name": "arbitrary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ridiculous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stupid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "awkward", "trans": [], "usphone": "", "ukphone": ""}, {"name": "clumsy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "careless", "trans": [], "usphone": "", "ukphone": ""}, {"name": "oblivious", "trans": [], "usphone": "", "ukphone": ""}, {"name": "naughty", "trans": [], "usphone": "", "ukphone": ""}, {"name": "greedy", "trans": [], "usphone": "", "ukphone": ""}, {"name": "obtrusive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "slothful", "trans": [], "usphone": "", "ukphone": ""}, {"name": "sluggish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "impulse", "trans": [], "usphone": "", "ukphone": ""}, {"name": "rigid", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stubborn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "stereotype", "trans": [], "usphone": "", "ukphone": ""}, {"name": "daily", "trans": [], "usphone": "", "ukphone": ""}, {"name": "monthly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "quarterly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "annual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "yearly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "anniversary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "era", "trans": [], "usphone": "", "ukphone": ""}, {"name": "present", "trans": [], "usphone": "", "ukphone": ""}, {"name": "contemporary", "trans": [], "usphone": "", "ukphone": ""}, {"name": "medieval", "trans": [], "usphone": "", "ukphone": ""}, {"name": "century", "trans": [], "usphone": "", "ukphone": ""}, {"name": "millennium", "trans": [], "usphone": "", "ukphone": ""}, {"name": "million", "trans": [], "usphone": "", "ukphone": ""}, {"name": "billion", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dozen", "trans": [], "usphone": "", "ukphone": ""}, {"name": "dawn", "trans": [], "usphone": "", "ukphone": ""}, {"name": "midday", "trans": [], "usphone": "", "ukphone": ""}, {"name": "midnight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "overnight", "trans": [], "usphone": "", "ukphone": ""}, {"name": "night", "trans": [], "usphone": "", "ukphone": ""}, {"name": "modern", "trans": [], "usphone": "", "ukphone": ""}, {"name": "recent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "up-to-date", "trans": [], "usphone": "", "ukphone": ""}, {"name": "first", "trans": [], "usphone": "", "ukphone": ""}, {"name": "secondly", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finally", "trans": [], "usphone": "", "ukphone": ""}, {"name": "finish", "trans": [], "usphone": "", "ukphone": ""}, {"name": "immediately", "trans": [], "usphone": "", "ukphone": ""}, {"name": "instant", "trans": [], "usphone": "", "ukphone": ""}, {"name": "moment", "trans": [], "usphone": "", "ukphone": ""}, {"name": "minute", "trans": [], "usphone": "", "ukphone": ""}, {"name": "prior", "trans": [], "usphone": "", "ukphone": ""}, {"name": "former", "trans": [], "usphone": "", "ukphone": ""}, {"name": "preceding", "trans": [], "usphone": "", "ukphone": ""}, {"name": "precedent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "foremost", "trans": [], "usphone": "", "ukphone": ""}, {"name": "opening", "trans": [], "usphone": "", "ukphone": ""}, {"name": "initially", "trans": [], "usphone": "", "ukphone": ""}, {"name": "intermediate", "trans": [], "usphone": "", "ukphone": ""}, {"name": "midst", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meantime", "trans": [], "usphone": "", "ukphone": ""}, {"name": "meanwhile", "trans": [], "usphone": "", "ukphone": ""}, {"name": "simultaneous", "trans": [], "usphone": "", "ukphone": ""}, {"name": "ongoing", "trans": [], "usphone": "", "ukphone": ""}, {"name": "everlasting", "trans": [], "usphone": "", "ukphone": ""}, {"name": "permanent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "punctual", "trans": [], "usphone": "", "ukphone": ""}, {"name": "duration", "trans": [], "usphone": "", "ukphone": ""}, {"name": "consecutive", "trans": [], "usphone": "", "ukphone": ""}, {"name": "periodically", "trans": [], "usphone": "", "ukphone": ""}, {"name": "imminent", "trans": [], "usphone": "", "ukphone": ""}, {"name": "incidentally", "trans": [], "usphone": "", "ukphone": ""}]