<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <link rel="icon" type="image/svg+xml" href="/logo.jpg"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Typing Word</title>
    <script>
      +(function () {
        function rem() {
          let html = document.documentElement;
          let max = parseInt('2048rem');
          let min = parseInt('2048rem');
          let width;
          if (window.innerWidth < min) {
            width = min;
          } else {
            width = window.innerWidth;
          }
          // console.log('width', width)
          html.style.fontSize = width / max + 'px';
        }

        rem();
        window.addEventListener('resize', rem);
      })();
    </script>
    <script>

    </script>
    <script>
      if (!location.href.includes('localhost')
        && !location.href.includes('192.168')
        && !location.href.includes('172.16')
        && !location.href.includes('10.0')
      ) {
        var _hmt = _hmt || [];
        (function () {
          var hm = document.createElement("script");
          hm.src = "https://hm.baidu.com/hm.js?3dae52fcd5375a19905462e4ad3eb54e";
          var s = document.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(hm, s);
        })();
      }
    </script>
</head>
<body>
<div id="app"></div>
<script type="module" src="/src/main.ts"></script>
</body>
</html>
