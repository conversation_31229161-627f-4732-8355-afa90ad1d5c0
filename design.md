# Typing Word - 界面和功能详细设计文档

## 项目概述

Typing Word 是一个基于 Vue 3 开发的在线背单词应用，支持单词记忆、文章练习、生词本管理等功能。项目采用现代化的前端技术栈，提供了丰富的学习功能和良好的用户体验。

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3 + TypeScript
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **UI组件**: Element Plus + 自定义组件
- **构建工具**: Vite
- **样式处理**: SCSS
- **国际化**: Vue I18n
- **数据持久化**: LocalForage + LocalStorage
- **虚拟滚动**: Vue Virtual Scroller

### 项目结构
```
src/
├── assets/          # 静态资源
├── components/      # 通用组件
├── hooks/          # 组合式函数
├── locales/        # 国际化文件
├── pages/          # 页面组件
├── stores/         # 状态管理
├── types.ts        # 类型定义
├── utils/          # 工具函数
└── router.ts       # 路由配置
```

## 核心数据结构

### Word (单词)
```typescript
type Word = {
  name: string,        // 单词名称
  usphone: string,     // 美式音标
  ukphone: string,     // 英式音标
  trans: string[]      // 翻译列表
  checked?: boolean,   // 是否选中
  id?: any,           // 唯一标识
}
```

### Dict (词典)
```typescript
interface Dict {
  id: string,                    // 词典ID
  name: string,                  // 词典名称
  description: string,           // 描述
  sort: Sort,                    // 排序方式
  originWords: Word[],           // 原始单词列表
  words: Word[],                 // 当前单词列表
  chapterWordNumber: number,     // 每章单词数量
  chapterWords: Word[][],        // 分章节单词
  residueWords: Word[],          // 剩余单词
  chapterIndex: number,          // 当前章节索引
  wordIndex: number,             // 当前单词索引
  articles: Article[],           // 文章列表
  statistics: Statistics[],      // 统计数据
  isCustom: boolean,            // 是否自定义词典
  length: number,               // 词典长度
  type: DictType,               // 词典类型
  language: LanguageType,       // 语言类型
  translateLanguage: TranslateLanguageType, // 翻译语言
}
```

### Article (文章)
```typescript
interface Article {
  id: string,                        // 文章ID
  title: string,                     // 标题
  titleTranslate: string,            // 标题翻译
  text: string,                      // 文章内容
  textCustomTranslate: string,       // 自定义翻译
  textNetworkTranslate: string,      // 网络翻译
  textCustomTranslateIsFormat: boolean, // 自定义翻译是否格式化
  newWords: Word[],                  // 生词列表
  textAllWords: ArticleWord[],       // 文章所有单词
  sections: string[],                // 文章段落
  useTranslateType: TranslateType    // 使用的翻译类型
}
```

## 主要功能模块

### 1. 背单词功能

#### 1.1 核心特性
- **记忆模式**: 显示单词和释义，用户可以查看音标、听发音
- **默写模式**: 隐藏单词，用户需要输入正确的单词
- **音标显示**: 支持美式和英式音标切换
- **发音功能**: 支持美音、英音切换，可调节语速
- **错误统计**: 自动记录错误单词，生成错词本

#### 1.2 界面设计
- **主练习区域**: 居中显示当前单词/输入框
- **工具栏**: 顶部显示章节信息、设置按钮
- **底部状态栏**: 显示进度、统计信息、操作按钮
- **侧边面板**: 显示单词列表、释义详情

#### 1.3 交互逻辑
- **键盘快捷键**: 支持空格键播放发音、回车键下一个单词
- **自动跳过**: 可设置自动跳过简单词
- **重复练习**: 章节完成后可选择重复练习错误词
- **进度保存**: 自动保存学习进度

### 2. 背文章功能

#### 2.1 核心特性
- **逐句输入**: 按句子顺序输入文章内容
- **自动发音**: 输入正确后自动播放句子发音
- **译文对照**: 支持显示中英文对照
- **一键翻译**: 集成翻译API，支持自动翻译
- **生词提取**: 自动提取文章中的生词

#### 2.2 界面设计
- **文章显示区**: 显示当前句子和上下文
- **输入区域**: 底部输入框，支持实时校验
- **译文区域**: 可切换显示的翻译内容
- **进度指示**: 显示文章完成进度

#### 2.3 交互逻辑
- **实时校验**: 输入时实时检查正确性
- **错误提示**: 输入错误时高亮显示
- **自动播放**: 完成句子后自动播放发音
- **跳过功能**: 支持跳过困难句子

### 3. 词典管理

#### 3.1 内置词典
- **考试词汇**: CET-4、CET-6、GMAT、GRE、IELTS、SAT、TOEFL
- **学术词汇**: 考研英语、专业四级、专业八级、商务英语
- **编程词汇**: JavaScript API、Node.js API、Java API、Linux Command
- **日语词汇**: N1-N5、日语常见词
- **其他语言**: 德语词汇等

#### 3.2 自定义词典
- **导入功能**: 支持JSON、CSV格式导入
- **在线编辑**: 可在线添加、编辑、删除单词
- **批量操作**: 支持批量导入导出
- **分类管理**: 支持词典分类和标签

#### 3.3 界面设计
- **词典列表**: 左侧显示所有可用词典
- **词典详情**: 右侧显示选中词典的详细信息
- **编辑界面**: 模态框形式的编辑界面
- **搜索功能**: 支持词典和单词搜索

### 4. 生词本系统

#### 4.1 三本管理
- **生词本**: 用户主动收藏的单词
- **错词本**: 练习中输入错误的单词
- **简单词**: 标记为简单的单词，练习时自动跳过

#### 4.2 功能特性
- **自动收集**: 错误单词自动加入错词本
- **手动管理**: 支持手动添加/移除单词
- **智能过滤**: 练习时自动过滤简单词
- **统计分析**: 显示各本的单词数量和学习情况

#### 4.3 界面设计
- **标签切换**: 顶部标签切换不同的词本
- **单词列表**: 虚拟滚动显示大量单词
- **操作按钮**: 每个单词旁边的操作按钮
- **批量操作**: 支持批量选择和操作

### 5. 统计分析

#### 5.1 学习统计
- **练习时长**: 记录每次练习的时间
- **正确率**: 计算输入正确率
- **单词数量**: 统计练习的单词总数
- **错误分析**: 分析常见错误类型

#### 5.2 进度跟踪
- **日历视图**: 显示每日学习情况
- **进度条**: 显示词典学习进度
- **成就系统**: 学习里程碑和成就

#### 5.3 数据可视化
- **图表展示**: 使用图表显示学习趋势
- **热力图**: 显示学习活跃度
- **对比分析**: 不同时期的学习对比

### 6. 设置系统

#### 6.1 练习设置
- **章节设置**: 每章单词数量设置
- **重复设置**: 错误单词重复次数
- **跳过设置**: 简单词自动跳过
- **排序设置**: 单词排序方式

#### 6.2 音频设置
- **发音设置**: 美音/英音切换
- **音量控制**: 发音音量调节
- **键盘音效**: 打字音效选择
- **自动播放**: 自动播放设置

#### 6.3 界面设置
- **主题切换**: 明暗主题切换
- **字体设置**: 字体大小和样式
- **布局设置**: 界面布局调整
- **快捷键**: 自定义快捷键

#### 6.4 翻译设置
- **翻译源**: 选择翻译API
- **翻译语言**: 目标翻译语言
- **自动翻译**: 自动翻译开关

## 界面布局设计

### 1. 主练习页面 (Practice Page)

#### 布局结构
```
┌─────────────────────────────────────────┐
│                 Logo                     │
├─────────────────────────────────────────┤
│              Toolbar                     │
│  [章节] [设置] [音量] [翻译] [重复]      │
├─────────────────────────────────────────┤
│                                         │
│            Practice Area                │
│         (Word/Article Content)          │
│                                         │
├─────────────────────────────────────────┤
│               Footer                    │
│    [进度] [统计] [上一个] [下一个]       │
└─────────────────────────────────────────┘
```

#### 右侧面板
```
┌─────────────────┐
│   Right Panel   │
│                 │
│  ┌───────────┐  │
│  │ Word List │  │
│  │           │  │
│  │  • word1  │  │
│  │  • word2  │  │
│  │  • word3  │  │
│  └───────────┘  │
│                 │
│  ┌───────────┐  │
│  │ Settings  │  │
│  │           │  │
│  │ [Options] │  │
│  └───────────┘  │
└─────────────────┘
```

### 2. 词典管理页面 (Dict Page)

#### 布局结构
```
┌─────────────────────────────────────────┐
│          Header Navigation              │
│    [练习] [词典] [设置]                 │
├─────────────────────────────────────────┤
│                                         │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │             │  │                 │  │
│  │ Dict List   │  │  Dict Detail    │  │
│  │             │  │                 │  │
│  │ • CET-4     │  │  ┌───────────┐  │  │
│  │ • CET-6     │  │  │ Word List │  │  │
│  │ • TOEFL     │  │  │           │  │  │
│  │ • Custom    │  │  │  word1    │  │  │
│  │             │  │  │  word2    │  │  │
│  │             │  │  │  word3    │  │  │
│  │             │  │  └───────────┘  │  │
│  └─────────────┘  └─────────────────┘  │
│                                         │
└─────────────────────────────────────────┘
```

### 3. 移动端适配

#### 响应式设计
- **断点设置**: 1680px 以下使用移动端布局
- **触摸优化**: 增大按钮点击区域
- **手势支持**: 支持滑动切换单词
- **虚拟键盘**: 优化虚拟键盘体验

#### 移动端特殊功能
- **手写识别**: 集成 Tesseract.js 进行手写识别
- **语音输入**: 支持语音输入单词
- **离线缓存**: 支持离线使用

## 用户交互流程

### 1. 学习流程

#### 开始学习
1. 用户进入应用
2. 选择词典或继续上次学习
3. 选择学习模式（记忆/默写）
4. 开始练习

#### 练习过程
1. 显示当前单词/句子
2. 用户输入或查看
3. 系统验证并给出反馈
4. 记录统计数据
5. 进入下一个单词/句子

#### 完成章节
1. 显示章节统计
2. 处理错误单词
3. 提供选择：重复/下一章/结束

### 2. 词典管理流程

#### 选择词典
1. 打开词典管理页面
2. 浏览可用词典列表
3. 查看词典详情
4. 选择词典开始学习

#### 自定义词典
1. 创建新词典
2. 添加单词和释义
3. 设置词典属性
4. 保存并开始使用

### 3. 设置配置流程

#### 个性化设置
1. 打开设置页面
2. 选择设置分类
3. 调整相关参数
4. 保存设置并应用

## 数据持久化

### 1. 本地存储
- **LocalStorage**: 存储用户设置和简单数据
- **LocalForage**: 存储词典数据和学习记录
- **版本控制**: 数据结构版本管理和升级

### 2. 数据结构
```typescript
// 设置数据
interface SettingState {
  // 练习设置
  chapterWordNumber: number,
  // 音频设置
  pronunciation: 'us' | 'uk',
  // 界面设置
  theme: 'light' | 'dark',
  // 其他设置...
}

// 词典数据
interface BaseState {
  myDictList: Dict[],
  collectDictIds: string[],
  current: {
    index: number,
    practiceType: DictType,
  },
  simpleWords: string[],
}
```

### 3. 数据同步
- **自动保存**: 用户操作后自动保存
- **数据校验**: 加载时验证数据完整性
- **错误恢复**: 数据损坏时的恢复机制

## 性能优化

### 1. 虚拟滚动
- 使用 Vue Virtual Scroller 处理大量单词列表
- 减少 DOM 节点数量，提升渲染性能

### 2. 懒加载
- 词典数据按需加载
- 音频文件延迟加载

### 3. 缓存策略
- 音频文件缓存
- 翻译结果缓存
- 词典数据缓存

### 4. 代码分割
- 路由级别的代码分割
- 组件级别的异步加载

## 可访问性设计

### 1. 键盘导航
- 全键盘操作支持
- 合理的 Tab 顺序
- 快捷键支持

### 2. 屏幕阅读器
- 语义化 HTML 结构
- ARIA 标签支持
- 替代文本提供

### 3. 视觉设计
- 高对比度主题
- 字体大小调节
- 色彩无障碍设计

## 国际化支持

### 1. 多语言
- 中文界面（默认）
- 英文界面支持
- 其他语言扩展接口

### 2. 本地化
- 日期时间格式
- 数字格式
- 文化适应性调整

## 错误处理

### 1. 用户错误
- 输入验证和提示
- 友好的错误信息
- 操作撤销功能

### 2. 系统错误
- 网络错误处理
- 数据加载失败处理
- 降级方案

### 3. 调试支持
- 开发环境错误详情
- 生产环境错误上报
- 用户反馈机制

## 安全考虑

### 1. 数据安全
- 本地数据加密存储
- 敏感信息保护
- 数据传输安全

### 2. 隐私保护
- 用户数据最小化收集
- 数据使用透明化
- 用户控制权限

## 特色功能详解

### 1. 智能学习算法
- **遗忘曲线**: 基于艾宾浩斯遗忘曲线调整复习频率
- **难度评估**: 根据用户错误率动态调整单词难度
- **个性化推荐**: 基于学习历史推荐合适的词典和章节

### 2. 多模态学习
- **视觉学习**: 单词卡片、图像联想
- **听觉学习**: 发音练习、听力训练
- **触觉学习**: 手写输入、触摸交互

### 3. 社交功能
- **学习排行榜**: 与好友比较学习进度
- **成就分享**: 分享学习成就到社交媒体
- **学习小组**: 创建或加入学习小组

### 4. 游戏化元素
- **积分系统**: 完成学习任务获得积分
- **等级系统**: 根据学习时长和正确率提升等级
- **徽章收集**: 完成特定挑战获得徽章
- **连击奖励**: 连续正确答题获得额外奖励

## 技术实现细节

### 1. 状态管理架构
```typescript
// Pinia Store 结构
stores/
├── base.ts          // 基础数据状态
├── practice.ts      // 练习状态
├── runtime.ts       // 运行时状态
└── setting.ts       // 设置状态
```

### 2. 组件架构
```typescript
components/
├── article/         // 文章相关组件
├── dialog/          // 对话框组件
├── icon/           // 图标组件
├── list/           // 列表组件
├── toolbar/        // 工具栏组件
└── [基础组件]      // 通用基础组件
```

### 3. 路由设计
```typescript
routes = [
  { path: '/', redirect: '/practice' },
  { path: '/practice', component: Practice },
  { path: '/dict', component: Dict },
  { path: '/mobile', component: Mobile },
]
```

### 4. API 集成
- **翻译 API**: 百度翻译 API 集成
- **发音 API**: 有道词典发音 API
- **OCR API**: Tesseract.js 文字识别

## 部署和运维

### 1. 构建配置
- **开发环境**: Vite 开发服务器
- **生产构建**: 静态文件生成
- **Docker 支持**: 容器化部署

### 2. 性能监控
- **加载时间监控**: 页面加载性能跟踪
- **错误监控**: 运行时错误收集
- **用户行为分析**: 学习行为数据分析

### 3. 版本管理
- **语义化版本**: 遵循 SemVer 规范
- **自动化发布**: CI/CD 流水线
- **回滚机制**: 快速版本回滚

这份详细的设计文档涵盖了 Typing Word 项目的所有重要方面，包括技术架构、功能设计、用户体验、性能优化等，为项目的开发、维护和扩展提供了全面的指导。
