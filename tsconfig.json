{
  "compilerOptions": {
    "target": "ES6",
    "useDefineForClassFields": true,
    "allowJs": true,
    "module": "ESNext",
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ],
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "typeRoots": [
      "./node_modules/@types",
      "./node_modules"
    ],
    "types": [
      "vue/ref-macros",
      "vite/client",
      "element-plus/global"
    ],
    "baseUrl": "src",
    //This is because TypeScript does not resolve webpack aliases automatically.
    //For TS to resolve aliases, they should be added in tsconfig.json under compilerOptions.paths:
    // https://betterprogramming.pub/the-right-usage-of-aliases-in-webpack-typescript-4418327f47fa
    "paths": {
      "@/*": [
        "*"
      ]
    }
  },
  "include": [
    "src",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.d.ts",
    "src/**/*.vue",
    "auto-imports.d.ts",
    "src/vite-env.d.ts"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
