{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-tsc": "vue-tsc && vite build", "report": "vite build", "preview": "vite preview", "commit": "git-cz", "prepare": "husky install", "start": "vite", "test": "", "deploy": "push-dir --dir=dist --branch=gh-pages --cleanup", "i18n:write": "gulp i18nwrite"}, "dependencies": {"@opentranslate/baidu": "1.4.2", "@opentranslate/translator": "1.4.2", "axios": "1.5.0", "compromise": "14.10.0", "copy-to-clipboard": "3.3.3", "element-plus": "2.3.9", "file-saver": "2.0.5", "git-last-commit": "1.0.1", "hover.css": "2.3.2", "localforage": "1.10.0", "lodash-es": "4.17.21", "mitt": "3.0.1", "nanoid": "5.0.3", "pinia": "2.1.6", "sentence-splitter": "4.2.1", "tesseract.js": "4.1.1", "vue": "3.3.13", "vue-activity-calendar": "1.2.2", "vue-i18n": "9", "vue-router": "4", "vue-virtual-scroller": "2.0.0-beta.8"}, "devDependencies": {"@iconify/vue": "4.1.1", "@types/file-saver": "2.0.5", "@types/lodash-es": "4.17.9", "@types/uuid": "9.0.4", "@vitejs/plugin-vue": "4.2.3", "@vitejs/plugin-vue-jsx": "3.0.1", "@vue/compiler-sfc": "3.3.4", "commitizen": "4.3.0", "cz-conventional-changelog": "3.3.0", "esm": "3.2.25", "gulp": "4.0.2", "husky": "8.0.3", "push-dir": "0.4.1", "rollup-plugin-visualizer": "5.9.2", "sass": "1.64.2", "tslib": "2.6.2", "typescript": "5.4.5", "unplugin-auto-import": "0.16.6", "unplugin-vue-components": "0.25.2", "vite": "4.4.5", "vue-tsc": "1.8.5", "xlsx": "0.18.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}